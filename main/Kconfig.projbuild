menu "<PERSON><PERSON> Assistant"

config OTA_URL
    string "Default OTA URL"
    default "https://api.tenclass.net/xiaozhi/ota/"
    help
        The application will access this URL to check for new firmwares and server address.

config SPEAKING_FIRST_MUTE_TIME
    int "speaking first mute time"
    default 60
    help
        讲话之前静音时间

choice
    prompt "语言选择"
    default LANGUAGE_ZH_CN
    help
        Select device display language

    config LANGUAGE_ZH_CN
        bool "Chinese"
    config LANGUAGE_ZH_TW
        bool "Chinese Traditional"
    config LANGUAGE_EN_US
        bool "English"
    config LANGUAGE_JA_JP
        bool "Japanese"
endchoice

choice BOARD_TYPE
    prompt "Board Type"
    default BOARD_TYPE_BREAD_COMPACT_WIFI
    help
        Board type. 开发板类型
    config BOARD_TYPE_BREAD_COMPACT_WIFI
        bool "面包板新版接线（WiFi）"
    config BOARD_TYPE_BREAD_COMPACT_WIFI_LCD
        bool "面包板新版接线（WiFi）+ LCD"
    config BOARD_TYPE_BREAD_COMPACT_ML307
        bool "面包板新版接线（ML307 AT）"
    config BOARD_TYPE_BREAD_COMPACT_ESP32
        bool "面包板（WiFi） ESP32 DevKit"
    config BOARD_TYPE_BREAD_COMPACT_ESP32_LCD
        bool "面包板（WiFi+ LCD） ESP32 DevKit"
    config BOARD_TYPE_ESP32_CGC
        bool "ESP32 CGC"
    config BOARD_TYPE_ESP_BOX_3
        bool "ESP BOX 3"
    config BOARD_TYPE_ESP_BOX
        bool "ESP BOX"
    config BOARD_TYPE_ESP_BOX_LITE
        bool "ESP BOX Lite"        
    config BOARD_TYPE_KEVIN_BOX_1
        bool "Kevin Box 1"
    config BOARD_TYPE_KEVIN_BOX_2
        bool "Kevin Box 2"
    config BOARD_TYPE_KEVIN_C3
        bool "Kevin C3"
    config BOARD_TYPE_KEVIN_SP_V3_DEV
        bool "Kevin SP V3开发板"
    config BOARD_TYPE_KEVIN_SP_V4_DEV
        bool "Kevin SP V4开发板"
    config BOARD_TYPE_KEVIN_YUYING_313LCD
        bool "鱼鹰科技3.13LCD开发板"
    config BOARD_TYPE_LICHUANG_DEV
        bool "立创·实战派ESP32-S3开发板"
    config BOARD_TYPE_LICHUANG_C3_DEV
        bool "立创·实战派ESP32-C3开发板"
    config BOARD_TYPE_DF_K10
        bool "DFRobot 行空板 k10"
    config BOARD_TYPE_MAGICLICK_2P4
        bool "神奇按钮 Magiclick_2.4"
    config BOARD_TYPE_MAGICLICK_2P5
        bool "神奇按钮 Magiclick_2.5"
    config BOARD_TYPE_MAGICLICK_C3
        bool "神奇按钮 Magiclick_C3"
    config BOARD_TYPE_MAGICLICK_C3_V2
        bool "神奇按钮 Magiclick_C3_v2"
    config BOARD_TYPE_M5STACK_CORE_S3
        bool "M5Stack CoreS3"
    config BOARD_TYPE_ATOMS3_ECHO_BASE
        bool "AtomS3 + Echo Base"
    config BOARD_TYPE_ATOMS3R_ECHO_BASE
        bool "AtomS3R + Echo Base"
    config BOARD_TYPE_ATOMS3R_CAM_M12_ECHO_BASE
        bool "AtomS3R CAM/M12 + Echo Base"
    config BOARD_TYPE_ATOMMATRIX_ECHO_BASE
        bool "AtomMatrix + Echo Base"
    config BOARD_TYPE_XMINI_C3
        bool "虾哥 Mini C3"
    config BOARD_TYPE_ESP32S3_KORVO2_V3
        bool "ESP32S3_KORVO2_V3开发板"
    config BOARD_TYPE_ESP_SPARKBOT
        bool "ESP-SparkBot开发板"
    config BOARD_TYPE_ESP_SPOT_S3
        bool "ESP-Spot-S3"
    config BOARD_TYPE_ESP32S3_Touch_AMOLED_1_8
        bool "Waveshare ESP32-S3-Touch-AMOLED-1.8"
    config BOARD_TYPE_ESP32S3_Touch_LCD_1_85C
        bool "Waveshare ESP32-S3-Touch-LCD-1.85C"
    config BOARD_TYPE_ESP32S3_Touch_LCD_1_85
        bool "Waveshare ESP32-S3-Touch-LCD-1.85"
    config BOARD_TYPE_ESP32S3_Touch_LCD_1_46
        bool "Waveshare ESP32-S3-Touch-LCD-1.46"
    config BOARD_TYPE_ESP32S3_Touch_LCD_3_5
        bool "Waveshare ESP32-S3-Touch-LCD-3.5"
    config BOARD_TYPE_TUDOUZI
        bool "土豆子"
    config BOARD_TYPE_LILYGO_T_CIRCLE_S3
        bool "LILYGO T-Circle-S3"
    config BOARD_TYPE_LILYGO_T_CAMERAPLUS_S3
        bool "LILYGO T-CameraPlus-S3"
    config BOARD_TYPE_LILYGO_T_DISPLAY_S3_PRO_MVSRLORA
        bool "LILYGO T-Display-S3-Pro-MVSRLora"
    config BOARD_TYPE_LILYGO_T_DISPLAY_S3_PRO_MVSRLORA_NO_BATTERY
        bool "LILYGO T-Display-S3-Pro-MVSRLora_No_Battery"
    config BOARD_TYPE_MOVECALL_MOJI_ESP32S3
         bool "Movecall Moji 小智AI衍生版"
    config BOARD_TYPE_MOVECALL_CUICAN_ESP32S3
         bool "Movecall CuiCan 璀璨·AI吊坠"
    config BOARD_TYPE_ATK_DNESP32S3
        bool "正点原子DNESP32S3开发板"
    config BOARD_TYPE_ATK_DNESP32S3_BOX
        bool "正点原子DNESP32S3-BOX"
    config BOARD_TYPE_ATK_DNESP32S3_BOX0
        bool "正点原子DNESP32S3-BOX0"
    config BOARD_TYPE_ATK_DNESP32S3M_WIFI
        bool "正点原子DNESP32S3M-WIFI"
    config BOARD_TYPE_ATK_DNESP32S3M_4G
        bool "正点原子DNESP32S3M-4G"
    config BOARD_TYPE_DU_CHATX
        bool "嘟嘟开发板CHATX(wifi)"
    config BOARD_TYPE_ESP32S3_Taiji_Pi
        bool "太极小派esp32s3"
    config BOARD_TYPE_XINGZHI_Cube_0_85TFT_WIFI
        bool "无名科技星智0.85(WIFI)"
    config BOARD_TYPE_XINGZHI_Cube_0_85TFT_ML307
        bool "无名科技星智0.85(ML307)"
    config BOARD_TYPE_XINGZHI_Cube_0_96OLED_WIFI
        bool "无名科技星智0.96(WIFI)"
    config BOARD_TYPE_XINGZHI_Cube_0_96OLED_ML307
        bool "无名科技星智0.96(ML307)"
    config BOARD_TYPE_XINGZHI_Cube_1_54TFT_WIFI
        bool "无名科技星智1.54(WIFI)"
    config BOARD_TYPE_XINGZHI_Cube_1_54TFT_ML307
        bool "无名科技星智1.54(ML307)"
    config BOARD_TYPE_SENSECAP_WATCHER
        bool "SenseCAP Watcher"
    config BOARD_TYPE_DOIT_S3_AIBOX
        bool "四博智联AI陪伴盒子"
    config BOARD_TYPE_MIXGO_NOVA
        bool "元控·青春"
    config BOARD_TYPE_ESP_S3_LCD_EV_Board
        bool "乐鑫ESP S3 LCD EV Board开发板"
    config BOARD_TYPE_DOIT_AI_01_KIT
        bool "Doit-AI-01-Kit"
    config BOARD_TYPE_DOIT_AI_01_KIT_LCD
        bool "Doit-AI-01-Kit-lcd"
    config BOARD_TYPE_DOIT_AI_SPEAKER
        bool "Doit-AI-SPEAKER"
    config BOARD_TYPE_DOIT_ESP32S3_EYE_6824
        bool "doit-esp32s3-eye-6824"
    config BOARD_TYPE_DOIT_ESP32S3_EYE_8311
        bool "doit-esp32s3-eye-8311"
endchoice

choice ESP_S3_LCD_EV_Board_Version_TYPE
    depends on BOARD_TYPE_ESP_S3_LCD_EV_Board
    prompt "EV_BOARD Type"
    default ESP_S3_LCD_EV_Board_1p4
    help
        开发板硬件版本型号选择
    config ESP_S3_LCD_EV_Board_1p4
        bool "乐鑫ESP32_S3_LCD_EV_Board-MB_V1.4"
    config ESP_S3_LCD_EV_Board_1p5
        bool "乐鑫ESP32_S3_LCD_EV_Board-MB_V1.5"
endchoice

choice DISPLAY_OLED_TYPE
    depends on BOARD_TYPE_BREAD_COMPACT_WIFI || BOARD_TYPE_BREAD_COMPACT_ML307 || BOARD_TYPE_BREAD_COMPACT_ESP32
    prompt "OLED Type"
    default OLED_SSD1306_128X32
    help
        OLED 屏幕类型选择
    config OLED_SSD1306_128X32
        bool "SSD1306, 分辨率128*32"
    config OLED_SSD1306_128X64
        bool "SSD1306, 分辨率128*64"
    config OLED_SH1106_128X64
        bool "SH1106, 分辨率128*64"
endchoice

choice DISPLAY_LCD_TYPE
    depends on BOARD_TYPE_BREAD_COMPACT_WIFI_LCD || BOARD_TYPE_BREAD_COMPACT_ESP32_LCD || BOARD_TYPE_ESP32_CGC || BOARD_TYPE_DOIT_ESP32S3_EYE_6824 || BOARD_TYPE_DOIT_ESP32S3_EYE_8311
    prompt "LCD Type"
    default LCD_ST7789_240X320
    help
        屏幕类型选择
    config LCD_ST7789_240X320
        bool "ST7789, 分辨率240*320, IPS"
    config LCD_ST7789_240X320_NO_IPS
        bool "ST7789, 分辨率240*320, 非IPS"
    config LCD_ST7789_170X320
        bool "ST7789, 分辨率170*320"
    config LCD_ST7789_172X320
        bool "ST7789, 分辨率172*320"
    config LCD_ST7789_240X280
        bool "ST7789, 分辨率240*280"
    config LCD_ST7789_240X240
        bool "ST7789, 分辨率240*240"
    config LCD_ST7789_240X240_7PIN
        bool "ST7789, 分辨率240*240, 7PIN"
    config LCD_ST7789_240X135
        bool "ST7789, 分辨率240*135"
    config LCD_ST7735_128X160
        bool "ST7735, 分辨率128*160"
    config LCD_ST7735_128X128
        bool "ST7735, 分辨率128*128"
    config LCD_ST7796_320X480
        bool "ST7796, 分辨率320*480 IPS"
    config LCD_ST7796_320X480_NO_IPS
        bool "ST7796, 分辨率320*480, 非IPS"    
    config LCD_ILI9341_240X320
        bool "ILI9341, 分辨率240*320"
    config LCD_ILI9341_240X320_NO_IPS
        bool "ILI9341, 分辨率240*320, 非IPS"
    config LCD_GC9A01_240X240
        bool "GC9A01, 分辨率240*240, 圆屏"
    config LCD_GC9A01_160X160
        bool "GC9A01, 分辨率160*160, 0.71寸"
    config LCD_CUSTOM
        bool "自定义屏幕参数"
endchoice


choice DISPLAY_ESP32S3_KORVO2_V3
    depends on BOARD_TYPE_ESP32S3_KORVO2_V3
    prompt "ESP32S3_KORVO2_V3 LCD Type"
    default LCD_ST7789
    help
        屏幕类型选择
    config LCD_ST7789
        bool "ST7789, 分辨率240*280"
    config LCD_ILI9341
        bool "ILI9341, 分辨率240*320"
endchoice

config USE_EYE_STYLE_ES8311
    bool "使用魔眼界面风格(ES8311)"
    default n
    help
        使用魔眼界面风格(ES8311)

config USE_EYE_STYLE_VB6824
    bool "使用魔眼界面风格(VB6824)"
    default n
    help
        使用魔眼界面风格(VB6824)

config USE_WECHAT_MESSAGE_STYLE
    bool "使用微信聊天界面风格"
    default n
    help
        使用微信聊天界面风格

config USE_WAKE_WORD_DETECT
    bool "启用唤醒词检测"
    default y
    depends on IDF_TARGET_ESP32S3 && SPIRAM
    help
        需要 ESP32 S3 与 AFE 支持

config USE_AUDIO_PROCESSOR
    bool "启用音频降噪、增益处理"
    default y
    depends on IDF_TARGET_ESP32S3 && SPIRAM
    help
        需要 ESP32 S3 与 AFE 支持

config USE_DEVICE_AEC
    bool "在通话过程中启用设备端 AEC"
    default n
    depends on (BOARD_TYPE_DOIT_AI_01_KIT || BOARD_TYPE_DOIT_AI_01_KIT_LCD) || (USE_AUDIO_PROCESSOR && (BOARD_TYPE_ESP_BOX_3 || BOARD_TYPE_ESP_BOX || BOARD_TYPE_ESP_BOX_LITE || BOARD_TYPE_LICHUANG_DEV || BOARD_TYPE_ESP32S3_KORVO2_V3))
    help
        因为性能不够，不建议和微信聊天界面风格同时开启

config USE_SERVER_AEC
    bool "在通话过程中启用服务器端 AEC"
    default n
    depends on (BOARD_TYPE_DOIT_AI_01_KIT || BOARD_TYPE_DOIT_AI_01_KIT_LCD) || USE_AUDIO_PROCESSOR
    help
        启用服务器端 AEC，需要服务器支持

config USE_AUDIO_CODEC_ENCODE_OPUS
    depends on BOARD_TYPE_DOIT_AI_01_KIT || BOARD_TYPE_DOIT_AI_01_KIT_LCD || BOARD_TYPE_DOIT_ESP32S3_EYE_6824
    bool "use audio codec encode opus"
    default y

config USE_AUDIO_CODEC_DECODE_OPUS
    depends on BOARD_TYPE_DOIT_AI_01_KIT || BOARD_TYPE_DOIT_AI_01_KIT_LCD
    bool "use audio codec decode opus"
    default n

config TACKGROUND_TASK_STACK_SIZE
    int "background task stack size"
    default 2048 if ((IDF_TARGET_ESP32C2 || IDF_TARGET_ESP32C3) && (USE_AUDIO_CODEC_ENCODE_OPUS && USE_AUDIO_CODEC_DECODE_OPUS))
    default 8960 if ((IDF_TARGET_ESP32C2 || IDF_TARGET_ESP32C3) && (USE_AUDIO_CODEC_ENCODE_OPUS))
    default 26624 if ((IDF_TARGET_ESP32C2 || IDF_TARGET_ESP32C3))
    default 32768

config AUDIO_LOOP_TASK_STACK_SIZE
    int "audio_loop task stack size"
    default 2048 if IDF_TARGET_ESP32C2
    default 8192

endmenu
