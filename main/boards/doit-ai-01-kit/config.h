/*
 * @Description: 
 * @Author: cjs丶
 * @Date: 2025-05-13 14:15:23
 * @LastEditTime: 2025-06-11 09:34:58
 * @LastEditors: cjs丶
 */
#ifndef _BOARD_CONFIG_H_
#define _BOARD_CONFIG_H_

#include <driver/gpio.h>
// #define BUILTIN_LED_GPIO        GPIO_NUM_8

// #define INTER_BUTTON_GPIO       GPIO_NUM_3
// #define BOOT_BUTTON_GPIO        GPIO_NUM_9
// #define TALK_BUTTON_GPIO        GPIO_NUM_6

#define CODEC_TX_GPIO           GPIO_NUM_17
#define CODEC_RX_GPIO           GPIO_NUM_18

/* LCD size */
#define DISPLAY_WIDTH   (240)
#define DISPLAY_HEIGHT  (240)
/* LCD settings */
#define GC9A01_LCD_SPI1_NUM         (SPI3_HOST)
#define GC9A01_LCD_SPI2_NUM         (SPI2_HOST)
#define GC9A01_LCD_PIXEL_CLK_HZ    (20 * 1000 * 1000)
#define GC9A01_LCD_CMD_BITS        (8)
#define GC9A01_LCD_PARAM_BITS      (8)
#define GC9A01_LCD_COLOR_SPACE     (ESP_LCD_COLOR_SPACE_RGB)
#define GC9A01_LCD_BITS_PER_PIXEL  (16)
#define GC9A01_LCD_DRAW_BUFF_DOUBLE (1)
#define GC9A01_LCD_DRAW_BUFF_HEIGHT (240)
#define GC9A01_LCD_BL_ON_LEVEL     (1)
/* LCD-SPI pins */
#define DISPLAY_CLK_PIN       (GPIO_NUM_4)
#define DISPLAY_MOSI_PIN       (GPIO_NUM_5)
#define DISPLAY_RST_PIN        (GPIO_NUM_6)
#define DISPLAY_DC_PIN         (GPIO_NUM_3)
#define DISPLAY_CS_PIN         (GPIO_NUM_2)
#define DISPLAY_BACKLIGHT_PIN         (GPIO_NUM_NC)
#define GC9A01_SPI2_LCD_GPIO_MISO       (GPIO_NUM_NC)

#endif // _BOARD_CONFIG_H_
