
#include "wifi_board.h"
#include "board.h"
#include "audio_codecs/vb6824_audio_codec.h"
#include "display/lcd_display.h"
#include <esp_lcd_gc9a01.h>
#include "application.h"
#include "button.h"
#include "config.h"
#include "iot/thing_manager.h"
#include "led/single_led.h"
#include <wifi_station.h>
#include <esp_log.h>
#include <esp_lcd_panel_vendor.h>
#include <driver/spi_common.h>
#include "my_espnow.h"

#if defined(CONFIG_VB6824_OTA_SUPPORT) && CONFIG_VB6824_OTA_SUPPORT == 1
#include "mbedtls/md5.h"
#include <iomanip>
#include <sstream>
#include "system_info.h"
#include "vb6824.h"
#include "wifi_station.h"
#endif

#define TAG "CustomBoard"

class CustomBoard : public WifiBoard {
private:
    // uint8_t button_state;
    // Button boot_button_;    //配网按键
    // Button talk_button_;    //对讲按键
    // Button inter_button_;   //按键打断
    VbAduioCodec audio_codec;
    LcdDisplay* display;

    esp_lcd_panel_io_handle_t lcd_io = NULL;
    esp_lcd_panel_handle_t lcd_panel = NULL;

    void InitializeButtons() {
        // inter_button_.OnClick([this]() {
        //     ESP_LOGI(TAG,"inter button down");
        //     if (audio_codec.InOtaMode(1) == true) {
        //         ESP_LOGI(TAG, "OTA mode, do not enter chat");
        //         return;
        //     }
        //     auto &app = Application::GetInstance();
        //     app.ToggleChatState();
        // });
        // boot_button_.OnPressRepeat([this](uint16_t count) {
        //     ESP_LOGI(TAG,"boot button down");
        //     if(count >= 3){
        //         ESP_LOGI(TAG, "重新配网");
        //         ResetWifiConfiguration();
        //     }
        // });
        // // boot_button_.OnLongPress([this]() {
        // //     if (esp_timer_get_time() > 20 * 1000 * 1000) {
        // //         ESP_LOGI(TAG, "Long press, do not enter OTA mode %ld", (uint32_t)esp_timer_get_time());
        // //         return;
        // //     }
        // //    int ret = audio_codec.OtaStart(0); 
        // //     if(ret == VbAduioCodec::OTA_ERR_NOT_SUPPORT){
        // //         ESP_LOGW(TAG, "Please enable VB6824_OTA_SUPPORT");
        // //     }
        // // });
        // talk_button_.OnPressDown([this]() { 
        //     button_state = 1;
        //     if (send_param->broadcast==false) {
        //         if(send_param->is_wifi_connect){
        //                 auto &app = Application::GetInstance();
        //             app.CloseAudioChannel();
        //         }
        //         if (!send_param->broadcast && broadcast_task_handle != NULL) {
        //             ESP_LOGI(TAG, "start the broadcast");
        //             send_param->broadcast = true;
        //         }
        //     }
        // });
        // talk_button_.OnPressUp([this]() {   
        //     button_state = 0;
        //     if(send_param->is_wifi_connect){
        //                 auto &app = Application::GetInstance();
        //                 app.OpenAudioChannel();
        //             }
        //     if (send_param->broadcast && broadcast_task_handle != NULL) {
        //             ESP_LOGI(TAG, "end the broadcast");
        //             send_param->broadcast = false;
        //     }
        // });
    }

    // 物联网初始化，添加对 AI 可见设备
    void InitializeIot() {
        auto& thing_manager = iot::ThingManager::GetInstance();
        thing_manager.AddThing(iot::CreateThing("Speaker"));
    }

      // GC9A01-SPI2初始化-用于显示小智
    void InitializeSpi() {
        const spi_bus_config_t buscfg = {       
            .mosi_io_num = DISPLAY_MOSI_PIN,
            .miso_io_num = GPIO_NUM_NC,
            .sclk_io_num = DISPLAY_CLK_PIN,
            .quadwp_io_num = GPIO_NUM_NC,
            .quadhd_io_num = GPIO_NUM_NC,
            .max_transfer_sz = DISPLAY_WIDTH * DISPLAY_HEIGHT * sizeof(uint16_t), // 增大传输大小,
        };
        ESP_ERROR_CHECK(spi_bus_initialize(GC9A01_LCD_SPI1_NUM, &buscfg, SPI_DMA_CH_AUTO));
    }

    // GC9A01-SPI2初始化-用于显示魔眼
    void InitializeGc9a01Display() {
        ESP_LOGI(TAG, "Init GC9A01 display1");

        ESP_LOGI(TAG, "Install panel IO1");
        ESP_LOGD(TAG, "Install panel IO1");
        const esp_lcd_panel_io_spi_config_t io_config = {
            .cs_gpio_num = DISPLAY_CS_PIN,
            .dc_gpio_num = DISPLAY_DC_PIN,
            .spi_mode = 0,
            .pclk_hz = GC9A01_LCD_PIXEL_CLK_HZ,
            .trans_queue_depth = 10,
            .lcd_cmd_bits = GC9A01_LCD_CMD_BITS,
            .lcd_param_bits = GC9A01_LCD_PARAM_BITS,
            
    
        };
        esp_lcd_new_panel_io_spi((esp_lcd_spi_bus_handle_t)GC9A01_LCD_SPI1_NUM, &io_config, &lcd_io);
    
        ESP_LOGD(TAG, "Install LCD1 driver");
        esp_lcd_panel_dev_config_t panel_config = {
            .reset_gpio_num = DISPLAY_RST_PIN,
            .color_space = GC9A01_LCD_COLOR_SPACE,
            .bits_per_pixel = GC9A01_LCD_BITS_PER_PIXEL,
            
        };
        panel_config.rgb_endian = GC9A01_LCD_COLOR_SPACE;
        esp_lcd_new_panel_gc9a01(lcd_io, &panel_config, &lcd_panel);
        
        esp_lcd_panel_reset(lcd_panel);
        esp_lcd_panel_init(lcd_panel);
        esp_lcd_panel_invert_color(lcd_panel, true);
        esp_lcd_panel_disp_on_off(lcd_panel, true);
    }


public:
// CustomBoard() : boot_button_(BOOT_BUTTON_GPIO),talk_button_(TALK_BUTTON_GPIO), inter_button_(INTER_BUTTON_GPIO),audio_codec(CODEC_TX_GPIO, CODEC_RX_GPIO){          
CustomBoard() : audio_codec(CODEC_TX_GPIO, CODEC_RX_GPIO){          
        InitializeButtons();
        InitializeIot();
        InitializeSpi();
        InitializeGc9a01Display();

        audio_codec.OnWakeUp([this](const std::string& command) {
            if (command == std::string(vb6824_get_wakeup_word())){
                if(Application::GetInstance().GetDeviceState() != kDeviceStateListening){
                    Application::GetInstance().WakeWordInvoke("你好小智");
                }
            }else if (command == "开始配网"){
                ResetWifiConfiguration();
            }
        });
    }

    virtual AudioCodec* GetAudioCodec() override {
        return &audio_codec;
    }

    virtual Display* GetDisplay() override {
        return display;
    }

    //  virtual Backlight* GetBacklight() override {
    //     if (DISPLAY_BACKLIGHT_PIN != GPIO_NUM_NC) {
    //         static PwmBacklight backlight(DISPLAY_BACKLIGHT_PIN, DISPLAY_BACKLIGHT_OUTPUT_INVERT);
    //         return &backlight;
    //     }
    //     return nullptr;
    // }

    // virtual uint8_t GetButtonState() override {
    //     return button_state;
    // }

    // virtual Led* GetLed() override {
    //     static SingleLed led(BUILTIN_LED_GPIO);
    // return &led;
    // }
};

DECLARE_BOARD(CustomBoard);
