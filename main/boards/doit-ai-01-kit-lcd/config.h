/*
 * @Description: 
 * @Author: cjs丶
 * @Date: 2025-06-11 09:29:39
 * @LastEditTime: 2025-06-14 12:40:02
 * @LastEditors: cjs丶
 */
#ifndef _BOARD_CONFIG_H_
#define _BOARD_CONFIG_H_

#include <driver/gpio.h>

#define BOOT_BUTTON_GPIO        GPIO_NUM_0

#define CODEC_TX_GPIO           GPIO_NUM_10
#define CODEC_RX_GPIO           GPIO_NUM_18

#define LCD_ST7789

#define DISPLAY_BACKLIGHT_PIN GPIO_NUM_4
#define DISPLAY_MOSI_PIN      GPIO_NUM_1
#define DISPLAY_CLK_PIN       GPIO_NUM_0
#define DISPLAY_DC_PIN        GPIO_NUM_3
#define DISPLAY_RST_PIN       GPIO_NUM_2
#define DISPLAY_CS_PIN        GPIO_NUM_NC

#ifdef LCD_ST7789
#define DISPLAY_WIDTH   240
#define DISPLAY_HEIGHT  240
#define DISPLAY_MIRROR_X false
#define DISPLAY_MIRROR_Y false
#define DISPLAY_SWAP_XY false
#define DISPLAY_INVERT_COLOR    true
#define DISPLAY_RGB_ORDER  LCD_RGB_ELEMENT_ORDER_RGB
#define DISPLAY_OFFSET_X  0
#define DISPLAY_OFFSET_Y  0
#define DISPLAY_BACKLIGHT_OUTPUT_INVERT false
#endif

#endif // _BOARD_CONFIG_H_
