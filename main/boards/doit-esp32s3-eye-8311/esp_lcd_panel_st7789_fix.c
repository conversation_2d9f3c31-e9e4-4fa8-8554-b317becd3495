/*
 * SPDX-FileCopyrightText: 2021-2023 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#include "sdkconfig.h"
#include <stdlib.h>
#include <sys/cdefs.h>

#if CONFIG_LCD_ENABLE_DEBUG_LOG
// The local log level must be defined before including esp_log.h
// Set the maximum log level for this source file
#define LOG_LOCAL_LEVEL ESP_LOG_DEBUG
#endif

#include "driver/gpio.h"
#include "esp_check.h"
#include "esp_lcd_panel_commands.h"
#include "esp_lcd_panel_interface.h"
#include "esp_lcd_panel_io.h"
#include "esp_lcd_panel_ops.h"
#include "esp_lcd_panel_vendor.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

#define ST7789_CMD_RAMCTRL 0xb0
#define ST7789_DATA_LITTLE_ENDIAN_BIT (1 << 3)

static const char *TAG = "lcd_panel.st7789";

static esp_err_t panel_st7789_del(esp_lcd_panel_t *panel);
static esp_err_t panel_st7789_reset(esp_lcd_panel_t *panel);
static esp_err_t panel_st7789_init(esp_lcd_panel_t *panel);
static esp_err_t panel_st7789_draw_bitmap(esp_lcd_panel_t *panel, int x_start,
                                          int y_start, int x_end, int y_end,
                                          const void *color_data);
static esp_err_t panel_st7789_invert_color(esp_lcd_panel_t *panel,
                                           bool invert_color_data);
static esp_err_t panel_st7789_mirror(esp_lcd_panel_t *panel, bool mirror_x,
                                     bool mirror_y);
static esp_err_t panel_st7789_swap_xy(esp_lcd_panel_t *panel, bool swap_axes);
static esp_err_t panel_st7789_set_gap(esp_lcd_panel_t *panel, int x_gap,
                                      int y_gap);
static esp_err_t panel_st7789_disp_on_off(esp_lcd_panel_t *panel, bool off);
static esp_err_t panel_st7789_sleep(esp_lcd_panel_t *panel, bool sleep);

typedef struct {
  esp_lcd_panel_t base;
  esp_lcd_panel_io_handle_t io;
  int reset_gpio_num;
  bool reset_level;
  int x_gap;
  int y_gap;
  uint8_t fb_bits_per_pixel;
  uint8_t madctl_val; // save current value of LCD_CMD_MADCTL register
  uint8_t colmod_val; // save current value of LCD_CMD_COLMOD register
  uint8_t ramctl_val_1;
  uint8_t ramctl_val_2;
} st7789_panel_t;

esp_err_t
esp_lcd_new_panel_st7789(const esp_lcd_panel_io_handle_t io,
                         const esp_lcd_panel_dev_config_t *panel_dev_config,
                         esp_lcd_panel_handle_t *ret_panel) {
#if CONFIG_LCD_ENABLE_DEBUG_LOG
  esp_log_level_set(TAG, ESP_LOG_DEBUG);
#endif
  esp_err_t ret = ESP_OK;
  st7789_panel_t *st7789 = NULL;
  ESP_GOTO_ON_FALSE(io && panel_dev_config && ret_panel, ESP_ERR_INVALID_ARG,
                    err, TAG, "invalid argument");
  st7789 = calloc(1, sizeof(st7789_panel_t));
  ESP_GOTO_ON_FALSE(st7789, ESP_ERR_NO_MEM, err, TAG,
                    "no mem for st7789 panel");

  if (panel_dev_config->reset_gpio_num >= 0) {
    gpio_config_t io_conf = {
        .mode = GPIO_MODE_OUTPUT,
        .pin_bit_mask = 1ULL << panel_dev_config->reset_gpio_num,
    };
    ESP_GOTO_ON_ERROR(gpio_config(&io_conf), err, TAG,
                      "configure GPIO for RST line failed");
  }

  switch (panel_dev_config->rgb_ele_order) {
  case LCD_RGB_ELEMENT_ORDER_RGB:
    st7789->madctl_val = 0;
    break;
  case LCD_RGB_ELEMENT_ORDER_BGR:
    st7789->madctl_val |= LCD_CMD_BGR_BIT;
    break;
  default:
    ESP_GOTO_ON_FALSE(false, ESP_ERR_NOT_SUPPORTED, err, TAG,
                      "unsupported RGB element order");
    break;
  }

  uint8_t fb_bits_per_pixel = 0;
  switch (panel_dev_config->bits_per_pixel) {
  case 16: // RGB565
    st7789->colmod_val = 0x55;
    fb_bits_per_pixel = 16;
    break;
  case 18: // RGB666
    st7789->colmod_val = 0x66;
    // each color component (R/G/B) should occupy the 6 high bits of a byte,
    // which means 3 full bytes are required for a pixel
    fb_bits_per_pixel = 24;
    break;
  default:
    ESP_GOTO_ON_FALSE(false, ESP_ERR_NOT_SUPPORTED, err, TAG,
                      "unsupported pixel width");
    break;
  }

  st7789->ramctl_val_1 = 0x00;
  st7789->ramctl_val_2 = 0xf0; // Use big endian by default
  if ((panel_dev_config->data_endian) == LCD_RGB_DATA_ENDIAN_LITTLE) {
    // Use little endian
    st7789->ramctl_val_2 |= ST7789_DATA_LITTLE_ENDIAN_BIT;
  }

  st7789->io = io;
  st7789->fb_bits_per_pixel = fb_bits_per_pixel;
  st7789->reset_gpio_num = panel_dev_config->reset_gpio_num;
  st7789->reset_level = panel_dev_config->flags.reset_active_high;
  st7789->base.del = panel_st7789_del;
  st7789->base.reset = panel_st7789_reset;
  st7789->base.init = panel_st7789_init;
  st7789->base.draw_bitmap = panel_st7789_draw_bitmap;
  st7789->base.invert_color = panel_st7789_invert_color;
  st7789->base.set_gap = panel_st7789_set_gap;
  st7789->base.mirror = panel_st7789_mirror;
  st7789->base.swap_xy = panel_st7789_swap_xy;
  st7789->base.disp_on_off = panel_st7789_disp_on_off;
  st7789->base.disp_sleep = panel_st7789_sleep;
  *ret_panel = &(st7789->base);
  ESP_LOGD(TAG, "new st7789 panel @%p", st7789);

  return ESP_OK;

err:
  if (st7789) {
    if (panel_dev_config->reset_gpio_num >= 0) {
      gpio_reset_pin(panel_dev_config->reset_gpio_num);
    }
    free(st7789);
  }
  return ret;
}

static esp_err_t panel_st7789_del(esp_lcd_panel_t *panel) {
  st7789_panel_t *st7789 = __containerof(panel, st7789_panel_t, base);

  if (st7789->reset_gpio_num >= 0) {
    gpio_reset_pin(st7789->reset_gpio_num);
  }
  ESP_LOGD(TAG, "del st7789 panel @%p", st7789);
  free(st7789);
  return ESP_OK;
}

static esp_err_t panel_st7789_reset(esp_lcd_panel_t *panel) {
  st7789_panel_t *st7789 = __containerof(panel, st7789_panel_t, base);
  esp_lcd_panel_io_handle_t io = st7789->io;

  // perform hardware reset
  if (st7789->reset_gpio_num >= 0) {
    gpio_set_level(st7789->reset_gpio_num, st7789->reset_level);
    vTaskDelay(pdMS_TO_TICKS(10));
    gpio_set_level(st7789->reset_gpio_num, !st7789->reset_level);
    vTaskDelay(pdMS_TO_TICKS(10));
  } else { // perform software reset
    ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, LCD_CMD_SWRESET, NULL, 0),
                        TAG, "io tx param failed");
    vTaskDelay(
        pdMS_TO_TICKS(20)); // spec, wait at least 5m before sending new command
  }

  return ESP_OK;
}

static esp_err_t panel_st7789_init(esp_lcd_panel_t *panel) {
  st7789_panel_t *st7789 = __containerof(panel, st7789_panel_t, base);
  esp_lcd_panel_io_handle_t io = st7789->io;
  // LCD goes into sleep mode and display will be turned off after power on
  // reset, exit sleep mode first
    ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, 0x11, NULL, 0), TAG, "io tx param failed");
    vTaskDelay(pdMS_TO_TICKS(100));

    //--------------------------------------Display Setting------------------------------------------//
    ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, 0x3A, (uint8_t[]){0x05}, 1), TAG, "io tx param failed");
    ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, 0x21, NULL, 0), TAG, "io tx param failed");
    
    ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, 0x2A, (uint8_t[]){0x00, 0x00, 0x00, 0xEF}, 4), TAG, "io tx param failed");
    ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, 0x2B, (uint8_t[]){0x00, 0x00, 0x00, 0xEF}, 4), TAG, "io tx param failed");

    //--------------------------------ST7789V Frame rate setting----------------------------------//
    ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, 0xB2, (uint8_t[]){0x0B, 0x0B, 0x00, 0x33, 0x33}, 5), TAG, "io tx param failed");
    ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, 0xB7, (uint8_t[]){0x35}, 1), TAG, "io tx param failed");

    //---------------------------------ST7789V Power setting--------------------------------------//
    ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, 0xBB, (uint8_t[]){0x1F}, 1), TAG, "io tx param failed");
    ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, 0xC0, (uint8_t[]){0x2C}, 1), TAG, "io tx param failed");
    ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, 0xC2, (uint8_t[]){0x01}, 1), TAG, "io tx param failed");
    ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, 0xC3, (uint8_t[]){0x12}, 1), TAG, "io tx param failed");
    ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, 0xC4, (uint8_t[]){0x20}, 1), TAG, "io tx param failed");
    ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, 0xC6, (uint8_t[]){0x0F}, 1), TAG, "io tx param failed");
    ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, 0xD0, (uint8_t[]){0xA4, 0xA1}, 2), TAG, "io tx param failed");

    //--------------------------------ST7789V gamma setting--------------------------------------//
    ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, 0xE0, (uint8_t[]){0xD0, 0x08, 0x11, 0x08, 0x0C, 0x15, 0x39, 0x33, 0x50, 0x36, 0x13, 0x14, 0x29, 0x2D}, 14), TAG, "io tx param failed");
    ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, 0xE1, (uint8_t[]){0xD0, 0x08, 0x10, 0x08, 0x06, 0x06, 0x39, 0x44, 0x51, 0x0B, 0x16, 0x14, 0x2F, 0x31}, 14), TAG, "io tx param failed");

    ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, 0x29, NULL, 0), TAG, "io tx param failed");
  

  return ESP_OK;
}

static esp_err_t panel_st7789_draw_bitmap(esp_lcd_panel_t *panel, int x_start,
                                          int y_start, int x_end, int y_end,
                                          const void *color_data) {
  st7789_panel_t *st7789 = __containerof(panel, st7789_panel_t, base);
  esp_lcd_panel_io_handle_t io = st7789->io;

  x_start += st7789->x_gap;
  x_end += st7789->x_gap;
  y_start += st7789->y_gap;
  y_end += st7789->y_gap;

  // define an area of frame memory where MCU can access
  ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, LCD_CMD_CASET,
                                                (uint8_t[]){
                                                    (x_start >> 8) & 0xFF,
                                                    x_start & 0xFF,
                                                    ((x_end - 1) >> 8) & 0xFF,
                                                    (x_end - 1) & 0xFF,
                                                },
                                                4),
                      TAG, "io tx param failed");
  ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, LCD_CMD_RASET,
                                                (uint8_t[]){
                                                    (y_start >> 8) & 0xFF,
                                                    y_start & 0xFF,
                                                    ((y_end - 1) >> 8) & 0xFF,
                                                    (y_end - 1) & 0xFF,
                                                },
                                                4),
                      TAG, "io tx param failed");
  // transfer frame buffer
  size_t len =
      (x_end - x_start) * (y_end - y_start) * st7789->fb_bits_per_pixel / 8;
  ESP_RETURN_ON_ERROR(
      esp_lcd_panel_io_tx_color(io, LCD_CMD_RAMWR, color_data, len), TAG,
      "io tx color failed");

  return ESP_OK;
}

static esp_err_t panel_st7789_invert_color(esp_lcd_panel_t *panel,
                                           bool invert_color_data) {
  st7789_panel_t *st7789 = __containerof(panel, st7789_panel_t, base);
  esp_lcd_panel_io_handle_t io = st7789->io;
  int command = 0;
  if (invert_color_data) {
    command = LCD_CMD_INVON;
  } else {
    command = LCD_CMD_INVOFF;
  }
  ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, command, NULL, 0), TAG,
                      "io tx param failed");
  return ESP_OK;
}

static esp_err_t panel_st7789_mirror(esp_lcd_panel_t *panel, bool mirror_x,
                                     bool mirror_y) {
  st7789_panel_t *st7789 = __containerof(panel, st7789_panel_t, base);
  esp_lcd_panel_io_handle_t io = st7789->io;
  if (mirror_x) {
    st7789->madctl_val |= LCD_CMD_MX_BIT;
  } else {
    st7789->madctl_val &= ~LCD_CMD_MX_BIT;
  }
  if (mirror_y) {
    st7789->madctl_val |= LCD_CMD_MY_BIT;
  } else {
    st7789->madctl_val &= ~LCD_CMD_MY_BIT;
  }
  ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, LCD_CMD_MADCTL,
                                                (uint8_t[]){st7789->madctl_val},
                                                1),
                      TAG, "io tx param failed");
  return ESP_OK;
}

static esp_err_t panel_st7789_swap_xy(esp_lcd_panel_t *panel, bool swap_axes) {
  st7789_panel_t *st7789 = __containerof(panel, st7789_panel_t, base);
  esp_lcd_panel_io_handle_t io = st7789->io;
  if (swap_axes) {
    st7789->madctl_val |= LCD_CMD_MV_BIT;
  } else {
    st7789->madctl_val &= ~LCD_CMD_MV_BIT;
  }
  ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, LCD_CMD_MADCTL,
                                                (uint8_t[]){st7789->madctl_val},
                                                1),
                      TAG, "io tx param failed");
  return ESP_OK;
}

static esp_err_t panel_st7789_set_gap(esp_lcd_panel_t *panel, int x_gap,
                                      int y_gap) {
  st7789_panel_t *st7789 = __containerof(panel, st7789_panel_t, base);
  st7789->x_gap = x_gap;
  st7789->y_gap = y_gap;
  return ESP_OK;
}

static esp_err_t panel_st7789_disp_on_off(esp_lcd_panel_t *panel, bool on_off) {
  st7789_panel_t *st7789 = __containerof(panel, st7789_panel_t, base);
  esp_lcd_panel_io_handle_t io = st7789->io;
  int command = 0;
  if (on_off) {
    command = LCD_CMD_DISPON;
  } else {
    command = LCD_CMD_DISPOFF;
  }
  ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, command, NULL, 0), TAG,
                      "io tx param failed");
  return ESP_OK;
}

static esp_err_t panel_st7789_sleep(esp_lcd_panel_t *panel, bool sleep) {
  st7789_panel_t *st7789 = __containerof(panel, st7789_panel_t, base);
  esp_lcd_panel_io_handle_t io = st7789->io;
  int command = 0;
  if (sleep) {
    command = LCD_CMD_SLPIN;
  } else {
    command = LCD_CMD_SLPOUT;
  }
  ESP_RETURN_ON_ERROR(esp_lcd_panel_io_tx_param(io, command, NULL, 0), TAG,
                      "io tx param failed");
  vTaskDelay(pdMS_TO_TICKS(100));

  return ESP_OK;
}
