#pragma once

#include "lvgl.h"
#include "psram_force_allocator.h"
#include "esp_log.h"

/**
 * LVGL PSRAM包装器
 * 为LVGL对象创建提供PSRAM优先的包装函数
 */
class LVGLPSRAMWrapper {
public:
    /**
     * 初始化LVGL PSRAM包装器
     */
    static void Initialize();

    /**
     * 创建屏幕对象（强制PSRAM）
     */
    static lv_obj_t* CreateScreen();

    /**
     * 创建GIF对象（强制PSRAM）
     */
    static lv_obj_t* CreateGif(lv_obj_t* parent);

    /**
     * 安全删除LVGL对象
     */
    static void SafeDeleteObject(lv_obj_t* obj);

    /**
     * 批量创建对象时的内存优化
     */
    static void BeginBatchCreate();
    static void EndBatchCreate();

    /**
     * 强制LVGL垃圾回收
     */
    static void ForceCleanup();

    /**
     * 打印LVGL内存使用统计
     */
    static void PrintLVGLMemoryStats();

private:
    static const char* TAG;
    static bool initialized_;
    static bool batch_mode_;
    static size_t objects_created_;
    
    /**
     * 内存分配钩子函数
     */
    static void* PSRAMAllocHook(size_t size);
    static void PSRAMFreeHook(void* ptr);
};
