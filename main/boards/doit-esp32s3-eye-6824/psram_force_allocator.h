#pragma once

#include "esp_heap_caps.h"
#include "esp_log.h"
#include <cstdlib>

/**
 * PSRAM强制分配器
 * 通过重定义malloc来强制使用PSRAM，释放内部RAM压力
 */

#ifdef __cplusplus
extern "C" {
#endif

/**
 * 初始化PSRAM强制分配器
 */
void psram_force_init(void);

/**
 * 强制PSRAM分配的malloc
 * 除了极小的分配外，都使用PSRAM
 */
void* psram_force_malloc(size_t size);

/**
 * 强制PSRAM分配的calloc
 */
void* psram_force_calloc(size_t num, size_t size);

/**
 * 强制PSRAM分配的realloc
 */
void* psram_force_realloc(void* ptr, size_t size);

/**
 * 智能free
 */
void psram_force_free(void* ptr);

/**
 * 打印分配统计
 */
void psram_force_print_stats(void);

/**
 * 强制垃圾回收
 * 尝试整理内存碎片
 */
void psram_force_gc(void);

#ifdef __cplusplus
}
#endif

/**
 * C++包装器类
 */
#ifdef __cplusplus
class PSRAMForceAllocator {
public:
    static void Initialize() { psram_force_init(); }
    static void* Malloc(size_t size) { return psram_force_malloc(size); }
    static void* Calloc(size_t num, size_t size) { return psram_force_calloc(num, size); }
    static void* Realloc(void* ptr, size_t size) { return psram_force_realloc(ptr, size); }
    static void Free(void* ptr) { psram_force_free(ptr); }
    static void PrintStats() { psram_force_print_stats(); }
    static void GarbageCollect() { psram_force_gc(); }
};
#endif

// 可选：全局重定义malloc（需要谨慎使用）
#ifdef ENABLE_PSRAM_FORCE_GLOBAL
#define malloc(size) psram_force_malloc(size)
#define calloc(num, size) psram_force_calloc(num, size)
#define realloc(ptr, size) psram_force_realloc(ptr, size)
#define free(ptr) psram_force_free(ptr)
#endif
