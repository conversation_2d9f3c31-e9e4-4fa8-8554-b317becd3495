#include "psram_malloc.h"
#include "esp_log.h"
#include "esp_heap_caps.h"
#include <cstring>

static const char* TAG = "PSRAMMalloc";

// 统计信息
static struct {
    size_t psram_allocations;
    size_t internal_allocations;
    size_t psram_bytes;
    size_t internal_bytes;
    size_t failed_allocations;
} malloc_stats = {0};

// 配置参数
#define PSRAM_THRESHOLD_SIZE    1024    // 大于1KB的分配优先使用PSRAM
#define INTERNAL_RESERVE_SIZE   32768   // 保留32KB内部RAM

void psram_malloc_init() {
    ESP_LOGI(TAG, "Initializing PSRAM-priority malloc");
    
    // 检查PSRAM可用性
    size_t psram_size = heap_caps_get_total_size(MALLOC_CAP_SPIRAM);
    size_t internal_size = heap_caps_get_total_size(MALLOC_CAP_INTERNAL);
    
    ESP_LOGI(TAG, "Available memory:");
    ESP_LOGI(TAG, "  PSRAM: %zu bytes (%.1f MB)", psram_size, psram_size / (1024.0 * 1024.0));
    ESP_LOGI(TAG, "  Internal: %zu bytes (%.1f KB)", internal_size, internal_size / 1024.0);
    
    if (psram_size == 0) {
        ESP_LOGW(TAG, "PSRAM not available, will use internal RAM only");
    }
    
    // 重置统计信息
    memset(&malloc_stats, 0, sizeof(malloc_stats));
}

void* psram_malloc(size_t size) {
    if (size == 0) {
        return nullptr;
    }
    
    void* ptr = nullptr;
    size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    
    // 决策逻辑：
    // 1. 如果请求大小 >= PSRAM_THRESHOLD_SIZE 且 PSRAM可用，优先使用PSRAM
    // 2. 如果内部RAM剩余 < INTERNAL_RESERVE_SIZE，强制使用PSRAM
    // 3. 否则使用内部RAM
    
    bool use_psram = false;
    
    if (free_psram > 0) {
        if (size >= PSRAM_THRESHOLD_SIZE) {
            use_psram = true;
        } else if (free_internal < INTERNAL_RESERVE_SIZE + size) {
            use_psram = true;
            ESP_LOGD(TAG, "Using PSRAM due to low internal RAM: %zu bytes remaining", free_internal);
        }
    }
    
    if (use_psram) {
        // 尝试PSRAM分配
        ptr = heap_caps_malloc(size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
        if (ptr) {
            malloc_stats.psram_allocations++;
            malloc_stats.psram_bytes += size;
            ESP_LOGV(TAG, "PSRAM malloc: %zu bytes at %p", size, ptr);
        } else {
            ESP_LOGD(TAG, "PSRAM allocation failed, trying internal RAM");
        }
    }
    
    if (!ptr) {
        // 回退到内部RAM或者直接使用内部RAM
        ptr = heap_caps_malloc(size, MALLOC_CAP_INTERNAL | MALLOC_CAP_8BIT);
        if (ptr) {
            malloc_stats.internal_allocations++;
            malloc_stats.internal_bytes += size;
            ESP_LOGV(TAG, "Internal malloc: %zu bytes at %p", size, ptr);
        } else {
            malloc_stats.failed_allocations++;
            ESP_LOGE(TAG, "Failed to allocate %zu bytes", size);
        }
    }
    
    return ptr;
}

void* psram_calloc(size_t num, size_t size) {
    size_t total_size = num * size;
    void* ptr = psram_malloc(total_size);
    if (ptr) {
        memset(ptr, 0, total_size);
    }
    return ptr;
}

void* psram_realloc(void* ptr, size_t size) {
    if (!ptr) {
        return psram_malloc(size);
    }
    
    if (size == 0) {
        psram_free(ptr);
        return nullptr;
    }
    
    // 检查原指针的内存类型
    bool ptr_in_psram = heap_caps_check_integrity(MALLOC_CAP_SPIRAM, false) && 
                        heap_caps_get_allocated_size(ptr) > 0;
    
    void* new_ptr = nullptr;
    
    if (ptr_in_psram) {
        // 原来在PSRAM，尝试PSRAM realloc
        new_ptr = heap_caps_realloc(ptr, size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
        if (!new_ptr) {
            // PSRAM realloc失败，手动复制到内部RAM
            new_ptr = heap_caps_malloc(size, MALLOC_CAP_INTERNAL | MALLOC_CAP_8BIT);
            if (new_ptr) {
                size_t old_size = heap_caps_get_allocated_size(ptr);
                memcpy(new_ptr, ptr, (size < old_size) ? size : old_size);
                heap_caps_free(ptr);
            }
        }
    } else {
        // 原来在内部RAM
        if (size >= PSRAM_THRESHOLD_SIZE && heap_caps_get_free_size(MALLOC_CAP_SPIRAM) > size) {
            // 大内存且PSRAM可用，迁移到PSRAM
            new_ptr = heap_caps_malloc(size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
            if (new_ptr) {
                size_t old_size = heap_caps_get_allocated_size(ptr);
                memcpy(new_ptr, ptr, (size < old_size) ? size : old_size);
                heap_caps_free(ptr);
            }
        } else {
            // 继续使用内部RAM
            new_ptr = heap_caps_realloc(ptr, size, MALLOC_CAP_INTERNAL | MALLOC_CAP_8BIT);
        }
    }
    
    if (!new_ptr) {
        malloc_stats.failed_allocations++;
        ESP_LOGE(TAG, "Failed to realloc %zu bytes", size);
    }
    
    return new_ptr;
}

void psram_free(void* ptr) {
    if (ptr) {
        heap_caps_free(ptr);
        ESP_LOGV(TAG, "Free: %p", ptr);
    }
}

void psram_malloc_stats() {
    ESP_LOGI(TAG, "=== PSRAM Malloc Statistics ===");
    ESP_LOGI(TAG, "PSRAM allocations: %zu (%zu bytes)", 
             malloc_stats.psram_allocations, malloc_stats.psram_bytes);
    ESP_LOGI(TAG, "Internal allocations: %zu (%zu bytes)", 
             malloc_stats.internal_allocations, malloc_stats.internal_bytes);
    ESP_LOGI(TAG, "Failed allocations: %zu", malloc_stats.failed_allocations);
    
    size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    ESP_LOGI(TAG, "Current free memory:");
    ESP_LOGI(TAG, "  PSRAM: %zu bytes", free_psram);
    ESP_LOGI(TAG, "  Internal: %zu bytes", free_internal);
}
