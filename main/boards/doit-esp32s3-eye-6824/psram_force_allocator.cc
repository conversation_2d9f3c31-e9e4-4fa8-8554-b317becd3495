#include "psram_force_allocator.h"
#include <cstring>

static const char* TAG = "PSRAMForce";

// 统计信息
static struct {
    size_t psram_allocs;
    size_t internal_allocs;
    size_t psram_bytes;
    size_t internal_bytes;
    size_t failed_allocs;
    size_t force_internal_count; // 强制使用内部RAM的次数
} stats = {0};

// 配置
#define FORCE_INTERNAL_THRESHOLD    64      // 小于64字节强制内部RAM
#define INTERNAL_EMERGENCY_RESERVE  8192    // 紧急保留8KB内部RAM
#define PSRAM_MIN_AVAILABLE        1024     // PSRAM最小可用1KB

void psram_force_init(void) {
    ESP_LOGI(TAG, "Initializing PSRAM Force Allocator");
    
    size_t psram_total = heap_caps_get_total_size(MALLOC_CAP_SPIRAM);
    size_t internal_total = heap_caps_get_total_size(MALLOC_CAP_INTERNAL);
    
    ESP_LOGI(TAG, "Memory configuration:");
    ESP_LOGI(TAG, "  PSRAM: %zu bytes (%.1f MB)", psram_total, psram_total / (1024.0 * 1024.0));
    ESP_LOGI(TAG, "  Internal: %zu bytes (%.1f KB)", internal_total, internal_total / 1024.0);
    ESP_LOGI(TAG, "Force allocation strategy:");
    ESP_LOGI(TAG, "  < %d bytes -> Internal RAM", FORCE_INTERNAL_THRESHOLD);
    ESP_LOGI(TAG, "  >= %d bytes -> PSRAM (if available)", FORCE_INTERNAL_THRESHOLD);
    ESP_LOGI(TAG, "  Emergency reserve: %d bytes internal", INTERNAL_EMERGENCY_RESERVE);
    
    // 重置统计
    memset(&stats, 0, sizeof(stats));
}

void* psram_force_malloc(size_t size) {
    if (size == 0) {
        return nullptr;
    }
    
    void* ptr = nullptr;
    bool use_psram = false;
    
    size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    
    // 决策逻辑：激进的PSRAM使用策略
    if (size < FORCE_INTERNAL_THRESHOLD) {
        // 极小分配：使用内部RAM（但检查是否紧急）
        if (free_internal > INTERNAL_EMERGENCY_RESERVE + size) {
            use_psram = false;
        } else if (free_psram > PSRAM_MIN_AVAILABLE) {
            use_psram = true;
            stats.force_internal_count++;
            ESP_LOGD(TAG, "Emergency: forcing small alloc (%zu bytes) to PSRAM", size);
        }
    } else {
        // 大分配：优先PSRAM
        if (free_psram > PSRAM_MIN_AVAILABLE) {
            use_psram = true;
        } else {
            ESP_LOGW(TAG, "PSRAM low, using internal for %zu bytes", size);
        }
    }
    
    // 执行分配
    if (use_psram) {
        ptr = heap_caps_malloc(size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
        if (ptr) {
            stats.psram_allocs++;
            stats.psram_bytes += size;
            ESP_LOGV(TAG, "PSRAM: %zu bytes at %p", size, ptr);
        } else {
            ESP_LOGD(TAG, "PSRAM alloc failed, trying internal");
        }
    }
    
    if (!ptr) {
        // 回退到内部RAM
        ptr = heap_caps_malloc(size, MALLOC_CAP_INTERNAL | MALLOC_CAP_8BIT);
        if (ptr) {
            stats.internal_allocs++;
            stats.internal_bytes += size;
            ESP_LOGV(TAG, "Internal: %zu bytes at %p", size, ptr);
        } else {
            stats.failed_allocs++;
            ESP_LOGE(TAG, "Failed to allocate %zu bytes", size);
            
            // 紧急情况：尝试垃圾回收后重试
            psram_force_gc();
            ptr = heap_caps_malloc(size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
            if (ptr) {
                ESP_LOGW(TAG, "Allocation succeeded after GC: %zu bytes", size);
                stats.psram_allocs++;
                stats.psram_bytes += size;
            }
        }
    }
    
    return ptr;
}

void* psram_force_calloc(size_t num, size_t size) {
    size_t total_size = num * size;
    void* ptr = psram_force_malloc(total_size);
    if (ptr) {
        memset(ptr, 0, total_size);
    }
    return ptr;
}

void* psram_force_realloc(void* ptr, size_t size) {
    if (!ptr) {
        return psram_force_malloc(size);
    }
    
    if (size == 0) {
        psram_force_free(ptr);
        return nullptr;
    }
    
    // 检查原指针位置
    bool ptr_in_psram = false;
    if (heap_caps_get_free_size(MALLOC_CAP_SPIRAM) < heap_caps_get_total_size(MALLOC_CAP_SPIRAM)) {
        // 简单检查：如果PSRAM有使用，假设大指针在PSRAM中
        size_t ptr_addr = (size_t)ptr;
        if (ptr_addr >= 0x3C000000 && ptr_addr < 0x3E000000) { // ESP32S3 PSRAM地址范围
            ptr_in_psram = true;
        }
    }
    
    void* new_ptr = nullptr;
    
    // 优先在相同内存类型中realloc
    if (ptr_in_psram) {
        new_ptr = heap_caps_realloc(ptr, size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    } else {
        // 检查是否应该迁移到PSRAM
        if (size >= FORCE_INTERNAL_THRESHOLD && heap_caps_get_free_size(MALLOC_CAP_SPIRAM) > size + PSRAM_MIN_AVAILABLE) {
            new_ptr = heap_caps_malloc(size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
            if (new_ptr) {
                size_t old_size = heap_caps_get_allocated_size(ptr);
                memcpy(new_ptr, ptr, (size < old_size) ? size : old_size);
                heap_caps_free(ptr);
                ESP_LOGD(TAG, "Migrated %zu bytes from internal to PSRAM", size);
            }
        } else {
            new_ptr = heap_caps_realloc(ptr, size, MALLOC_CAP_INTERNAL | MALLOC_CAP_8BIT);
        }
    }
    
    if (!new_ptr) {
        // 最后尝试：任意内存类型
        new_ptr = heap_caps_realloc(ptr, size, MALLOC_CAP_DEFAULT);
        if (!new_ptr) {
            stats.failed_allocs++;
            ESP_LOGE(TAG, "Realloc failed: %zu bytes", size);
        }
    }
    
    return new_ptr;
}

void psram_force_free(void* ptr) {
    if (ptr) {
        heap_caps_free(ptr);
    }
}

void psram_force_print_stats(void) {
    ESP_LOGI(TAG, "=== PSRAM Force Allocator Statistics ===");
    ESP_LOGI(TAG, "PSRAM allocations: %zu (%zu bytes)", stats.psram_allocs, stats.psram_bytes);
    ESP_LOGI(TAG, "Internal allocations: %zu (%zu bytes)", stats.internal_allocs, stats.internal_bytes);
    ESP_LOGI(TAG, "Failed allocations: %zu", stats.failed_allocs);
    ESP_LOGI(TAG, "Emergency PSRAM usage: %zu times", stats.force_internal_count);
    
    // 当前内存状态
    size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    size_t total_internal = heap_caps_get_total_size(MALLOC_CAP_INTERNAL);
    size_t total_psram = heap_caps_get_total_size(MALLOC_CAP_SPIRAM);
    
    ESP_LOGI(TAG, "Current memory state:");
    ESP_LOGI(TAG, "  Internal: %zu/%zu bytes (%.1f%% used)", 
             total_internal - free_internal, total_internal,
             (float)(total_internal - free_internal) / total_internal * 100);
    ESP_LOGI(TAG, "  PSRAM: %zu/%zu bytes (%.1f%% used)", 
             total_psram - free_psram, total_psram,
             (float)(total_psram - free_psram) / total_psram * 100);
    
    // 分配效率
    size_t total_allocs = stats.psram_allocs + stats.internal_allocs;
    if (total_allocs > 0) {
        float psram_ratio = (float)stats.psram_allocs / total_allocs * 100;
        ESP_LOGI(TAG, "PSRAM allocation ratio: %.1f%%", psram_ratio);
    }
}

void psram_force_gc(void) {
    ESP_LOGI(TAG, "Performing garbage collection...");
    
    // 记录GC前的内存状态
    size_t free_internal_before = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t free_psram_before = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    
    // ESP32的垃圾回收主要是整理内存碎片
    // 这里我们可以尝试一些策略来释放内存
    
    // 1. 强制LVGL垃圾回收（如果可用）
    #if LV_USE_GC
    lv_gc_collect();
    #endif
    
    // 2. 尝试分配和释放一些大块内存来整理碎片
    void* temp_ptrs[10];
    size_t temp_sizes[] = {64*1024, 32*1024, 16*1024, 8*1024, 4*1024};
    
    for (int i = 0; i < 5; i++) {
        temp_ptrs[i] = heap_caps_malloc(temp_sizes[i], MALLOC_CAP_SPIRAM);
        if (temp_ptrs[i]) {
            heap_caps_free(temp_ptrs[i]);
        }
    }
    
    // 记录GC后的内存状态
    size_t free_internal_after = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t free_psram_after = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    
    ESP_LOGI(TAG, "GC completed:");
    ESP_LOGI(TAG, "  Internal: %zu -> %zu bytes (%+ld)", 
             free_internal_before, free_internal_after, 
             (long)(free_internal_after - free_internal_before));
    ESP_LOGI(TAG, "  PSRAM: %zu -> %zu bytes (%+ld)", 
             free_psram_before, free_psram_after, 
             (long)(free_psram_after - free_psram_before));
}
