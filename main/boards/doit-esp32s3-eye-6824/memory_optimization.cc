#include "memory_optimization.h"
#include <cstring>

const char* MemoryOptimization::TAG = "MemoryOpt";
bool MemoryOptimization::initialized_ = false;
MemoryOptimization::Stats MemoryOptimization::stats_ = {0};

void MemoryOptimization::Initialize() {
    if (initialized_) {
        return;
    }
    
    ESP_LOGI(TAG, "Initializing Memory Optimization");
    
    // 检查PSRAM可用性
    size_t psram_size = heap_caps_get_total_size(MALLOC_CAP_SPIRAM);
    size_t internal_size = heap_caps_get_total_size(MALLOC_CAP_INTERNAL);
    
    ESP_LOGI(TAG, "Memory configuration:");
    ESP_LOGI(TAG, "  PSRAM: %zu bytes (%.1f MB)", psram_size, psram_size / (1024.0 * 1024.0));
    ESP_LOGI(TAG, "  Internal: %zu bytes (%.1f KB)", internal_size, internal_size / 1024.0);
    
    if (psram_size == 0) {
        ESP_LOGW(TAG, "PSRAM not available - optimization will be limited");
    }
    
    // 重置统计信息
    memset(&stats_, 0, sizeof(stats_));
    initialized_ = true;
    
    ESP_LOGI(TAG, "Memory optimization initialized");
}

void* MemoryOptimization::SmartMalloc(size_t size, const char* purpose) {
    if (!initialized_) {
        Initialize();
    }
    
    if (size == 0) {
        return nullptr;
    }
    
    void* ptr = nullptr;
    bool use_psram = false;
    
    // 决策逻辑
    size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    
    // 大于2KB的分配优先使用PSRAM
    if (size >= 2048 && free_psram > size) {
        use_psram = true;
    }
    // 如果内部RAM紧张（少于16KB），使用PSRAM
    else if (free_internal < 16384 && free_psram > size) {
        use_psram = true;
        ESP_LOGD(TAG, "Using PSRAM due to low internal RAM: %zu bytes", free_internal);
    }
    // 特定用途强制使用PSRAM
    else if (purpose && (strstr(purpose, "display") || strstr(purpose, "buffer") || strstr(purpose, "gif"))) {
        if (free_psram > size) {
            use_psram = true;
            ESP_LOGD(TAG, "Using PSRAM for %s allocation", purpose);
        }
    }
    
    if (use_psram) {
        ptr = heap_caps_malloc(size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
        if (ptr) {
            ESP_LOGV(TAG, "PSRAM alloc: %zu bytes at %p (%s)", size, ptr, purpose ? purpose : "general");
        }
    }
    
    if (!ptr) {
        // 回退到内部RAM
        ptr = heap_caps_malloc(size, MALLOC_CAP_INTERNAL | MALLOC_CAP_8BIT);
        if (ptr) {
            ESP_LOGV(TAG, "Internal alloc: %zu bytes at %p (%s)", size, ptr, purpose ? purpose : "general");
        } else {
            ESP_LOGE(TAG, "Failed to allocate %zu bytes (%s)", size, purpose ? purpose : "general");
        }
    }
    
    if (ptr) {
        stats_.smart_alloc_count++;
        stats_.smart_alloc_bytes += size;
    }
    
    return ptr;
}

void MemoryOptimization::SmartFree(void* ptr) {
    if (ptr) {
        heap_caps_free(ptr);
    }
}

void* MemoryOptimization::AllocateDisplayBuffer(size_t size) {
    if (!initialized_) {
        Initialize();
    }
    
    ESP_LOGI(TAG, "Allocating display buffer: %zu bytes", size);
    
    // 显示缓冲区强制使用PSRAM
    void* buffer = heap_caps_malloc(size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    
    if (buffer) {
        stats_.display_buffer_count++;
        stats_.display_buffer_bytes += size;
        ESP_LOGI(TAG, "Display buffer allocated in PSRAM: %p (%zu bytes)", buffer, size);
    } else {
        ESP_LOGW(TAG, "PSRAM allocation failed, trying internal RAM");
        buffer = heap_caps_malloc(size, MALLOC_CAP_INTERNAL | MALLOC_CAP_8BIT);
        if (buffer) {
            ESP_LOGW(TAG, "Display buffer allocated in internal RAM: %p (%zu bytes)", buffer, size);
        } else {
            ESP_LOGE(TAG, "Failed to allocate display buffer: %zu bytes", size);
        }
    }
    
    return buffer;
}

void MemoryOptimization::FreeDisplayBuffer(void* buffer) {
    if (buffer) {
        ESP_LOGD(TAG, "Freeing display buffer: %p", buffer);
        heap_caps_free(buffer);
    }
}

void MemoryOptimization::PrintStats() {
    ESP_LOGI(TAG, "=== Memory Optimization Statistics ===");
    ESP_LOGI(TAG, "Smart allocations: %zu (%zu bytes)", 
             stats_.smart_alloc_count, stats_.smart_alloc_bytes);
    ESP_LOGI(TAG, "Display buffers: %zu (%zu bytes)", 
             stats_.display_buffer_count, stats_.display_buffer_bytes);
    
    // 当前内存状态
    size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    size_t min_free_internal = heap_caps_get_minimum_free_size(MALLOC_CAP_INTERNAL);
    
    ESP_LOGI(TAG, "Current memory state:");
    ESP_LOGI(TAG, "  Internal: %zu bytes free (min: %zu)", free_internal, min_free_internal);
    ESP_LOGI(TAG, "  PSRAM: %zu bytes free", free_psram);
    
    // 内存使用率
    size_t total_internal = heap_caps_get_total_size(MALLOC_CAP_INTERNAL);
    size_t total_psram = heap_caps_get_total_size(MALLOC_CAP_SPIRAM);
    
    if (total_internal > 0) {
        float internal_usage = (float)(total_internal - free_internal) / total_internal * 100;
        ESP_LOGI(TAG, "  Internal usage: %.1f%%", internal_usage);
    }
    
    if (total_psram > 0) {
        float psram_usage = (float)(total_psram - free_psram) / total_psram * 100;
        ESP_LOGI(TAG, "  PSRAM usage: %.1f%%", psram_usage);
    }
}

bool MemoryOptimization::CheckMemoryHealth() {
    size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    
    bool healthy = true;
    
    // 检查内部RAM是否过低
    if (free_internal < 10240) { // 10KB
        ESP_LOGW(TAG, "⚠️  Low internal RAM: %zu bytes", free_internal);
        healthy = false;
    }
    
    // 检查PSRAM是否可用
    if (free_psram == 0) {
        ESP_LOGW(TAG, "⚠️  PSRAM not available or full");
        healthy = false;
    }
    
    return healthy;
}

void MemoryOptimization::GetRecommendedBufferConfig(uint32_t screen_width, 
                                                   uint32_t screen_height,
                                                   uint32_t* buffer_size,
                                                   bool* enable_double_buffer,
                                                   bool* use_psram) {
    if (!initialized_) {
        Initialize();
    }
    
    size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    
    uint32_t full_screen_size = screen_width * screen_height * 2; // RGB565
    
    // 推荐配置
    if (free_psram > full_screen_size * 2) {
        // PSRAM充足，使用大缓冲区
        *buffer_size = full_screen_size / 4; // 1/4屏幕
        *enable_double_buffer = true;
        *use_psram = true;
        ESP_LOGI(TAG, "Recommended: Large PSRAM buffers (1/4 screen, double buffered)");
    } else if (free_psram > full_screen_size) {
        // PSRAM适中，使用中等缓冲区
        *buffer_size = full_screen_size / 8; // 1/8屏幕
        *enable_double_buffer = true;
        *use_psram = true;
        ESP_LOGI(TAG, "Recommended: Medium PSRAM buffers (1/8 screen, double buffered)");
    } else if (free_internal > full_screen_size / 4) {
        // 使用内部RAM
        *buffer_size = full_screen_size / 16; // 1/16屏幕
        *enable_double_buffer = false;
        *use_psram = false;
        ESP_LOGI(TAG, "Recommended: Small internal buffers (1/16 screen, single buffered)");
    } else {
        // 内存紧张，最小配置
        *buffer_size = full_screen_size / 32; // 1/32屏幕
        *enable_double_buffer = false;
        *use_psram = false;
        ESP_LOGW(TAG, "Recommended: Minimal buffers (1/32 screen) - memory constrained");
    }
    
    ESP_LOGI(TAG, "Buffer config: size=%lu, double=%s, psram=%s", 
             *buffer_size, *enable_double_buffer ? "yes" : "no", *use_psram ? "yes" : "no");
}
