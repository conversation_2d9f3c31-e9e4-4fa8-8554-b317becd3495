# 20FPS性能测试指南

## 🧪 测试方法

### 1. 基础帧率测试
```cpp
void test_20fps_basic() {
    ESP_LOGI("PERF_TEST", "=== 20FPS Basic Test Started ===");
    
    const char* emotions[] = {"happy", "sad", "thinking", "neutral"};
    int emotion_count = sizeof(emotions) / sizeof(emotions[0]);
    
    for(int cycle = 0; cycle < 5; cycle++) {
        for(int i = 0; i < emotion_count; i++) {
            uint32_t start_time = esp_timer_get_time() / 1000; // ms
            
            display->SetSyncEmotion(emotions[i]);
            
            uint32_t end_time = esp_timer_get_time() / 1000; // ms
            uint32_t switch_time = end_time - start_time;
            
            ESP_LOGI("PERF_TEST", "Cycle %d, Emotion %s: switch time %lu ms", 
                     cycle + 1, emotions[i], switch_time);
            
            vTaskDelay(pdMS_TO_TICKS(2000)); // 2秒观察时间
        }
    }
    
    ESP_LOGI("PERF_TEST", "=== 20FPS Basic Test Completed ===");
}
```

### 2. 快速切换压力测试
```cpp
void test_20fps_stress() {
    ESP_LOGI("PERF_TEST", "=== 20FPS Stress Test Started ===");
    
    const char* emotions[] = {"happy", "sad"};
    
    for(int i = 0; i < 50; i++) {
        uint32_t start_time = esp_timer_get_time() / 1000;
        
        display->SetSyncEmotion(emotions[i % 2]);
        
        uint32_t end_time = esp_timer_get_time() / 1000;
        uint32_t switch_time = end_time - start_time;
        
        if(switch_time > 100) { // 超过100ms认为异常
            ESP_LOGW("PERF_TEST", "Slow switch detected: %lu ms", switch_time);
        }
        
        vTaskDelay(pdMS_TO_TICKS(500)); // 快速切换
        
        if(i % 10 == 0) {
            ESP_LOGI("PERF_TEST", "Stress test progress: %d/50", i);
        }
    }
    
    ESP_LOGI("PERF_TEST", "=== 20FPS Stress Test Completed ===");
}
```

### 3. 长时间稳定性测试
```cpp
void test_20fps_stability() {
    ESP_LOGI("PERF_TEST", "=== 20FPS Stability Test Started (10 minutes) ===");
    
    const char* emotions[] = {"happy", "thinking", "sad", "neutral"};
    int emotion_count = sizeof(emotions) / sizeof(emotions[0]);
    
    uint32_t start_time = esp_timer_get_time() / 1000000; // 秒
    uint32_t test_duration = 10 * 60; // 10分钟
    int cycle = 0;
    
    while(true) {
        uint32_t current_time = esp_timer_get_time() / 1000000;
        if(current_time - start_time >= test_duration) {
            break;
        }
        
        display->SetSyncEmotion(emotions[cycle % emotion_count]);
        cycle++;
        
        // 每分钟报告一次状态
        if(cycle % 12 == 0) { // 每12次切换约1分钟 (5秒间隔)
            uint32_t elapsed = current_time - start_time;
            ESP_LOGI("PERF_TEST", "Stability test: %lu/%lu seconds, %d cycles", 
                     elapsed, test_duration, cycle);
            
            // 检查内存使用
            ESP_LOGI("PERF_TEST", "Free internal: %d bytes, Free PSRAM: %d bytes",
                     heap_caps_get_free_size(MALLOC_CAP_INTERNAL),
                     heap_caps_get_free_size(MALLOC_CAP_SPIRAM));
        }
        
        vTaskDelay(pdMS_TO_TICKS(5000)); // 5秒间隔
    }
    
    ESP_LOGI("PERF_TEST", "=== 20FPS Stability Test Completed: %d cycles ===", cycle);
}
```

## 📊 性能指标监控

### 关键指标
```cpp
void monitor_performance() {
    // 1. 内存使用
    size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    size_t min_free_internal = heap_caps_get_minimum_free_size(MALLOC_CAP_INTERNAL);
    
    ESP_LOGI("MONITOR", "Memory - Internal: %zu bytes (min: %zu), PSRAM: %zu bytes", 
             free_internal, min_free_internal, free_psram);
    
    // 2. 任务状态
    TaskStatus_t task_status;
    if(eTaskGetState(sync_task_handle_) == eRunning) {
        ESP_LOGI("MONITOR", "GIF sync task: Running");
    } else {
        ESP_LOGW("MONITOR", "GIF sync task: Not running");
    }
    
    // 3. 队列状态
    UBaseType_t queue_spaces = uxQueueSpacesAvailable(gif_sync_queue_);
    ESP_LOGI("MONITOR", "Sync queue spaces available: %d", queue_spaces);
}
```

## ✅ 成功标准

### 1. 帧率测试
- [ ] GIF切换时间 < 50ms
- [ ] 动画播放流畅，无明显卡顿
- [ ] 双屏完全同步，无时间差

### 2. 稳定性测试
- [ ] 10分钟连续运行无崩溃
- [ ] 无看门狗超时错误
- [ ] 内存使用稳定，无内存泄漏

### 3. 系统资源
- [ ] 内部RAM使用 < 80%
- [ ] PSRAM使用合理
- [ ] CPU使用率适中

## 🚨 问题排查

### 如果出现卡顿
1. 检查是否有看门狗超时
2. 监控内存使用情况
3. 确认任务优先级设置
4. 检查SPI传输是否正常

### 如果同步失败
1. 检查同步任务是否运行
2. 确认LVGL锁获取成功
3. 检查队列消息发送
4. 验证任务优先级设置

### 如果内存不足
1. 减小缓冲区大小
2. 禁用双缓冲
3. 检查内存泄漏
4. 考虑使用内部RAM

## 📈 性能调优建议

### 如果20FPS仍不够流畅
```cpp
// 尝试25FPS
#define GIF_LVGL_TIMER_PERIOD_MS    40      // 40ms = 25FPS
#define GIF_BUFFER_DIVISOR          6       // 更大缓冲区
```

### 如果系统不稳定
```cpp
// 降回15FPS
#define GIF_LVGL_TIMER_PERIOD_MS    67      // 67ms ≈ 15FPS
#define GIF_BUFFER_DIVISOR          10      // 较小缓冲区
```

### 如果内存紧张
```cpp
// 禁用双缓冲
#define GIF_ENABLE_DOUBLE_BUFFER    false
// 或使用内部RAM
#define GIF_USE_PSRAM_BUFFER        false
#define GIF_USE_DMA_BUFFER          true
```

## 🎯 测试流程

1. **编译并烧录** 20FPS配置
2. **运行基础测试** 验证基本功能
3. **执行压力测试** 检查快速切换性能
4. **进行稳定性测试** 确保长时间运行稳定
5. **监控系统资源** 确保资源使用合理
6. **记录测试结果** 为后续优化提供数据

## 📝 测试报告模板

```
=== 20FPS性能测试报告 ===
测试时间: [日期时间]
配置版本: 20FPS优化版本

基础功能测试:
- GIF切换时间: [平均时间] ms
- 双屏同步: [正常/异常]
- 动画流畅度: [流畅/卡顿]

压力测试:
- 50次快速切换: [成功/失败]
- 异常切换次数: [次数]
- 最大切换时间: [时间] ms

稳定性测试:
- 运行时长: [分钟]
- 崩溃次数: [次数]
- 看门狗超时: [有/无]

资源使用:
- 内部RAM最低: [字节]
- PSRAM使用: [字节]
- CPU使用率: [估算]

结论: [通过/需要调优/不通过]
建议: [具体建议]
```
