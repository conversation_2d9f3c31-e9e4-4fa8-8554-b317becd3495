# GIF性能优化测试说明

## 优化内容总结

### 1. 双屏GIF同步问题解决
- **问题**: 两个屏幕的GIF播放不同步，因为是顺序设置的
- **解决方案**: 
  - 创建专门的GIF同步任务，运行在核心0上
  - 使用队列机制统一处理GIF更新请求
  - 在同步任务中同时暂停、设置、重启两个屏幕的GIF播放

### 2. 性能优化和双核利用
- **LVGL任务优化**:
  - 提高任务优先级从1到5
  - 刷新率从50ms提升到16ms (约60FPS)
  - 增加任务堆栈大小到8192
  - 绑定LVGL任务到核心1

- **GIF同步任务**:
  - 运行在核心0上，与LVGL任务分离
  - 优先级6，高于LVGL任务
  - 专门处理GIF同步逻辑

- **显示缓冲优化**:
  - 启用双缓冲 (double_buffer = true)
  - 增大缓冲区大小到屏幕的1/4 (width * height / 4)
  - 使用PSRAM缓冲区，释放内部RAM压力
  - 不使用DMA缓冲区，避免PSRAM与DMA的兼容性问题

## 测试方法

### 1. 同步测试
```cpp
// 测试两个屏幕的GIF是否同步
display->SetSyncEmotion("happy");  // 应该看到两个屏幕同时开始播放
```

### 2. 性能测试
```cpp
// 测试刷新率和流畅度
display->SetEmotion("thinking");   // 观察动画是否流畅
display->SetEmotion("happy");      // 快速切换观察响应速度
```

### 3. 双核利用测试
- 在串口监视器中观察任务运行情况
- 应该看到LVGL任务运行在核心1，GIF同步任务运行在核心0

## 预期效果

1. **同步性**: 两个屏幕的GIF动画完全同步，没有时间差
2. **流畅度**: 动画播放更加流畅，减少卡顿现象
3. **响应速度**: GIF切换响应更快
4. **CPU利用**: 双核心分工，提高整体性能

## 监控指标

### 串口日志关键信息
```
I (xxx) EyeGifDisplay: LVGL port initialized with 60FPS target on core 1
I (xxx) EyeGifDisplay: Buffer configuration:
I (xxx) EyeGifDisplay:   - Buffer size: 6400 pixels (1/4 of screen)
I (xxx) EyeGifDisplay:   - Double buffer: enabled
I (xxx) EyeGifDisplay:   - DMA buffer: disabled
I (xxx) EyeGifDisplay:   - PSRAM buffer: enabled
I (xxx) EyeGifDisplay: GIF sync task created successfully on core 0
I (xxx) EyeGifDisplay: GIF sync task started on core x
I (xxx) EyeGifDisplay: Processing GIF sync message - sync_both: 1, target_screen: 0
I (xxx) EyeGifDisplay: Synchronizing GIF on both screens
I (xxx) EyeGifDisplay: Synchronized GIF update completed
```

### 性能指标
- 帧率: 目标60FPS
- 内存使用:
  - 内部RAM: 释放约25.6KB
  - PSRAM: 使用约51.2KB
  - 总体: 更大缓冲区，更好性能
- CPU使用: 双核分工，整体CPU使用更均衡

## 故障排除

### 如果同步仍有问题
1. 检查GIF同步任务是否正常启动
2. 检查队列消息是否正常发送和接收
3. 检查LVGL锁是否正常获取

### 如果性能没有提升
1. 检查双缓冲是否成功启用
2. 检查任务是否绑定到正确的核心
3. 检查内存是否足够分配大缓冲区

### 内存相关问题

#### PSRAM不可用
如果PSRAM不可用，可以切换回内部RAM配置：
```cpp
// 在gif_config.h中修改
#define GIF_BUFFER_DIVISOR          8       // 减小缓冲区
#define GIF_USE_DMA_BUFFER          true    // 启用DMA
#define GIF_USE_PSRAM_BUFFER        false   // 禁用PSRAM
```

#### 内存分配失败
如果仍出现内存分配失败，可以进一步减小缓冲区：
```cpp
#define GIF_BUFFER_DIVISOR          10      // 改为1/10屏幕
```

#### 内存使用监控
添加内存监控代码：
```cpp
ESP_LOGI(TAG, "Internal RAM: %d bytes", heap_caps_get_free_size(MALLOC_CAP_INTERNAL));
ESP_LOGI(TAG, "PSRAM: %d bytes", heap_caps_get_free_size(MALLOC_CAP_SPIRAM));
```
