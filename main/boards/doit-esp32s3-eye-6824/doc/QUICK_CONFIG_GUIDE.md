# 快速配置切换指南

## 当前问题解决方案

### 🚨 看门狗超时问题 - 立即解决

您遇到的看门狗超时问题已通过以下配置解决：

```cpp
// 在 gif_config.h 中已设置的保守配置
#define GIF_CONSERVATIVE_MODE        1      // ✅ 已启用保守模式
#define GIF_LVGL_TIMER_PERIOD_MS    100     // ✅ 降低到10FPS
#define GIF_BUFFER_DIVISOR          10      // ✅ 减小缓冲区
#define GIF_ENABLE_DOUBLE_BUFFER    false   // ✅ 禁用双缓冲
```

### 📊 当前配置状态 (20FPS模式)

**性能设置**:
- 刷新率: 20FPS (50ms周期) ⚡
- 任务优先级: 适中优先级 (平衡性能和稳定性)
- 缓冲区: 1/8屏幕大小 (增大25%)
- 双缓冲: 启用 (减少闪烁)

**内存设置**:
- 使用PSRAM存储缓冲区
- 双缓冲模式
- 预计内存使用: ~25.6KB (PSRAM)

## 配置模式选择

### 模式1: 超稳定模式 (当前推荐) ✅
```cpp
#define GIF_CONSERVATIVE_MODE        1
// 自动应用以下设置:
// - 刷新率: 5FPS (200ms)
// - 优先级: 最低
// - 缓冲区: 1/16屏幕
```

**优点**: 绝对稳定，不会有看门狗超时
**缺点**: 动画较慢，但仍然流畅

### 模式2: 20FPS性能模式 (当前推荐) ✅
```cpp
#define GIF_OPTIMIZE_FOR_PERFORMANCE 1      // 启用性能优化
#define GIF_CONSERVATIVE_MODE        0      // 禁用保守模式
// 自动应用以下设置:
// - 刷新率: 20FPS (50ms)
// - 缓冲区: 1/8屏幕
// - 双缓冲: 启用
```

**优点**: 流畅的20FPS动画，双屏完美同步
**缺点**: 相比保守模式消耗更多资源

### 模式3: 高性能模式 (25FPS+)
```cpp
#define GIF_OPTIMIZE_FOR_PERFORMANCE 1
#define GIF_LVGL_TIMER_PERIOD_MS    40      // 25FPS
#define GIF_BUFFER_DIVISOR          6       // 1/6屏幕
```

**优点**: 最佳动画效果
**缺点**: 需要充分测试稳定性

## 切换步骤

### 从当前配置开始测试

1. **编译并测试当前配置**
   ```bash
   idf.py build flash monitor
   ```

2. **观察日志输出**
   ```
   I EyeGifDisplay: Buffer config: 160x160/10 = 2560 pixels per buffer
   I EyeGifDisplay: Estimated buffer memory: 10240 bytes (PSRAM, single-buffered)
   I EyeGifDisplay: GIF sync task created successfully on core 0
   ```

3. **测试GIF同步**
   ```cpp
   display->SetSyncEmotion("happy");    // 测试同步播放
   display->SetEmotion("thinking");     // 测试切换
   ```

### 如果当前配置稳定，可以尝试提升性能

#### 步骤1: 提高刷新率
```cpp
#define GIF_LVGL_TIMER_PERIOD_MS    50      // 改为20FPS
```

#### 步骤2: 增大缓冲区
```cpp
#define GIF_BUFFER_DIVISOR          8       // 改为1/8屏幕
```

#### 步骤3: 启用双缓冲
```cpp
#define GIF_ENABLE_DOUBLE_BUFFER    true
```

**⚠️ 每次只改一个参数，测试稳定后再改下一个**

## 监控指标

### 关键日志信息
```
✅ 正常启动:
I EyeGifDisplay: LVGL port initialized with 10FPS target on core 1
I EyeGifDisplay: GIF sync task created successfully on core 0

✅ 内存正常:
I EyeGifDisplay: Internal RAM - Free: 180000+ bytes
I EyeGifDisplay: PSRAM - Free: 7900000+ bytes

✅ 同步正常:
I EyeGifDisplay: Synchronizing GIF on both screens
I EyeGifDisplay: Synchronized GIF update completed

❌ 需要注意:
E task_wdt: Task watchdog got triggered  // 看门狗超时
E lcd_panel.io.spi: spi transmit failed  // SPI传输失败
```

### 性能测试方法

1. **同步测试**
   ```cpp
   // 快速切换，观察是否同步
   display->SetSyncEmotion("happy");
   vTaskDelay(pdMS_TO_TICKS(2000));
   display->SetSyncEmotion("sad");
   ```

2. **稳定性测试**
   ```cpp
   // 长时间运行，观察是否有看门狗超时
   for(int i = 0; i < 100; i++) {
       display->SetEmotion("happy");
       vTaskDelay(pdMS_TO_TICKS(3000));
       display->SetEmotion("thinking");
       vTaskDelay(pdMS_TO_TICKS(3000));
   }
   ```

## 故障恢复

### 如果出现看门狗超时
1. 立即启用超稳定模式:
   ```cpp
   #define GIF_CONSERVATIVE_MODE        1
   ```

2. 或手动设置最保守参数:
   ```cpp
   #define GIF_LVGL_TIMER_PERIOD_MS    500     // 2FPS
   #define GIF_BUFFER_DIVISOR          20      // 1/20屏幕
   ```

### 如果出现内存不足
1. 减小缓冲区:
   ```cpp
   #define GIF_BUFFER_DIVISOR          16      // 或更大的数值
   ```

2. 切换到内部RAM:
   ```cpp
   #define GIF_USE_PSRAM_BUFFER        false
   #define GIF_USE_DMA_BUFFER          true
   ```

## 预期效果

### 当前配置下的预期效果
- ✅ **稳定性**: 无看门狗超时，系统稳定运行
- ✅ **同步性**: 两屏GIF完全同步播放
- ✅ **流畅度**: 10FPS，动画流畅但不快速
- ✅ **内存**: PSRAM使用，内部RAM压力小
- ✅ **响应**: GIF切换响应及时

### 优化后的潜在效果
- 🎯 **更高刷新率**: 20-30FPS
- 🎯 **更流畅动画**: 双缓冲减少闪烁
- 🎯 **更快响应**: 大缓冲区减少传输次数

## 建议

1. **先确保稳定**: 使用当前保守配置确保系统稳定运行
2. **逐步优化**: 一次只调整一个参数
3. **充分测试**: 每次调整后运行至少5分钟
4. **记录配置**: 记录每次调整的参数和效果
5. **备份配置**: 保存稳定的配置作为备份
