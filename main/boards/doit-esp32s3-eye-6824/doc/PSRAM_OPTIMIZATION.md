# PSRAM优化配置说明

## 配置变更

### 1. 缓冲区配置优化
- **缓冲区大小**: 从1/8屏幕增加到1/4屏幕 (更大的缓冲区)
- **存储位置**: 从内部RAM切换到PSRAM
- **双缓冲**: 保持启用状态

### 2. 内存分配策略
```cpp
// 之前的配置 (内部RAM + DMA)
.buff_dma = 1,      // 使用DMA缓冲区
.buff_spiram = 0,   // 不使用SPIRAM

// 新的配置 (PSRAM)
.buff_dma = 0,      // 不使用DMA缓冲区
.buff_spiram = 1,   // 使用PSRAM缓冲区
```

## 优势

### 1. 内存使用优化
- **释放内部RAM**: 将大缓冲区移到PSRAM，释放宝贵的内部RAM
- **更大缓冲区**: 可以使用更大的缓冲区而不担心内存不足
- **减少内存碎片**: 内部RAM用于小对象，PSRAM用于大缓冲区

### 2. 性能提升
- **减少刷新次数**: 更大的缓冲区意味着每次可以传输更多数据
- **更流畅的动画**: 减少因缓冲区不足导致的卡顿
- **双核优化**: 内部RAM释放后，双核任务调度更高效

### 3. 稳定性改善
- **内存分配成功率**: PSRAM空间更大，分配失败概率降低
- **系统稳定性**: 内部RAM压力减小，系统更稳定

## 配置参数

### gif_config.h 中的关键配置
```cpp
// 缓冲区配置
#define GIF_BUFFER_DIVISOR          4       // 1/4屏幕大小
#define GIF_ENABLE_DOUBLE_BUFFER    true    // 启用双缓冲
#define GIF_USE_DMA_BUFFER          false   // 不使用DMA
#define GIF_USE_PSRAM_BUFFER        true    // 使用PSRAM

// 任务配置保持不变
#define GIF_LVGL_TASK_CORE          1       // LVGL在核心1
#define GIF_SYNC_TASK_CORE          0       // 同步任务在核心0
```

## 内存使用对比

### 之前配置 (内部RAM + DMA)
- 单屏缓冲区: 160×160÷8 = 3,200 像素 × 2字节 = 6.4KB
- 双缓冲: 6.4KB × 2 = 12.8KB
- 双屏总计: 12.8KB × 2 = 25.6KB (内部RAM)

### 新配置 (PSRAM)
- 单屏缓冲区: 160×160÷4 = 6,400 像素 × 2字节 = 12.8KB
- 双缓冲: 12.8KB × 2 = 25.6KB
- 双屏总计: 25.6KB × 2 = 51.2KB (PSRAM)

### 效果
- **内部RAM释放**: 25.6KB
- **PSRAM使用**: 51.2KB (ESP32S3通常有8MB PSRAM)
- **缓冲区增大**: 2倍

## 性能监控

### 关键日志信息
```
I (xxx) EyeGifDisplay: Buffer configuration:
I (xxx) EyeGifDisplay:   - Buffer size: 6400 pixels (1/4 of screen)
I (xxx) EyeGifDisplay:   - Double buffer: enabled
I (xxx) EyeGifDisplay:   - DMA buffer: disabled
I (xxx) EyeGifDisplay:   - PSRAM buffer: enabled
```

### 内存使用检查
```cpp
// 检查内部RAM使用
int free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
ESP_LOGI(TAG, "Free internal RAM: %d bytes", free_internal);

// 检查PSRAM使用
int free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
ESP_LOGI(TAG, "Free PSRAM: %d bytes", free_psram);
```

## 故障排除

### 如果PSRAM初始化失败
1. 检查硬件是否支持PSRAM
2. 检查menuconfig中PSRAM配置
3. 回退到内部RAM配置

### 如果性能下降
1. PSRAM访问速度比内部RAM慢，这是正常的
2. 但更大的缓冲区应该能补偿这个差异
3. 如果仍有问题，可以调整缓冲区大小

### 配置切换
如需切换回内部RAM配置，修改gif_config.h：
```cpp
#define GIF_BUFFER_DIVISOR          8       // 恢复到1/8
#define GIF_USE_DMA_BUFFER          true    // 启用DMA
#define GIF_USE_PSRAM_BUFFER        false   // 禁用PSRAM
```

## 预期效果

1. **同步性**: 保持完美同步
2. **流畅度**: 更加流畅，减少卡顿
3. **内存**: 内部RAM使用减少，系统更稳定
4. **扩展性**: 为未来功能扩展预留更多内部RAM空间
