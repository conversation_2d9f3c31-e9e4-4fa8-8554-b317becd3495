# 双屏GIF显示故障排除指南

## 常见问题及解决方案

### 1. 看门狗超时 (Task watchdog timeout) ⚠️

**症状**:
```
E (xxxx) task_wdt: Task watchdog got triggered. The following tasks/users did not reset the watchdog in time:
E (xxxx) task_wdt:  - IDLE1 (CPU 1)
E (xxxx) task_wdt: CPU 1: taskLVGL
```

**原因**: LVGL任务执行时间过长，阻塞了看门狗重置

**解决方案**:

#### 方案1: 启用保守模式 (推荐)
```cpp
// 在 gif_config.h 中设置
#define GIF_CONSERVATIVE_MODE        1      // 启用保守模式
```

#### 方案2: 手动调整参数
```cpp
// 降低刷新率
#define GIF_LVGL_TIMER_PERIOD_MS    200     // 5FPS

// 降低任务优先级
#define GIF_LVGL_TASK_PRIORITY      1       // 最低优先级

// 减小缓冲区
#define GIF_BUFFER_DIVISOR          16      // 1/16屏幕
```

### 2. SPI传输失败

**症状**:
```
E (xxxx) lcd_panel.io.spi: panel_io_spi_tx_color(395): spi transmit (queue) color failed
E (xxxx) gc9a01: panel_gc9a01_draw_bitmap(459): send color failed
```

**原因**: 
- SPI总线繁忙
- 缓冲区过大导致传输超时
- PSRAM访问速度限制

**解决方案**:

#### 方案1: 减小缓冲区
```cpp
#define GIF_BUFFER_DIVISOR          16      // 使用更小的缓冲区
```

#### 方案2: 切换回内部RAM
```cpp
#define GIF_USE_PSRAM_BUFFER        false   // 禁用PSRAM
#define GIF_USE_DMA_BUFFER          true    // 启用DMA
```

#### 方案3: 增加传输延时
在同步任务中增加延时：
```cpp
vTaskDelay(pdMS_TO_TICKS(50));  // 增加到50ms
```

### 3. 内存不足

**症状**:
```
E (xxxx) heap: heap_caps_alloc(xxx) failed
```

**解决方案**:

#### 检查内存使用
```cpp
ESP_LOGI(TAG, "Internal RAM: %d", heap_caps_get_free_size(MALLOC_CAP_INTERNAL));
ESP_LOGI(TAG, "PSRAM: %d", heap_caps_get_free_size(MALLOC_CAP_SPIRAM));
```

#### 启用内存优化模式
```cpp
#define GIF_OPTIMIZE_FOR_MEMORY      1
#define GIF_CONSERVATIVE_MODE        1
```

### 4. GIF不同步

**症状**: 两个屏幕的GIF播放时间不一致

**解决方案**:

#### 检查同步任务状态
```cpp
// 查看日志中是否有这些信息
I EyeGifDisplay: GIF sync task created successfully on core 0
I EyeGifDisplay: Synchronizing GIF on both screens
I EyeGifDisplay: Synchronized GIF update completed
```

#### 增加队列超时时间
```cpp
#define GIF_QUEUE_SEND_TIMEOUT_MS   1000    // 增加到1秒
```

### 5. 显示异常或黑屏

**可能原因**:
- LVGL锁获取失败
- 显示器初始化失败
- 缓冲区配置错误

**解决方案**:

#### 检查显示器初始化
```cpp
// 查看日志
I EyeGifDisplay: Display1 created: 0x[address]
I EyeGifDisplay: Display2 created: 0x[address]
```

#### 重置为最小配置
```cpp
#define GIF_BUFFER_DIVISOR          20      // 最小缓冲区
#define GIF_ENABLE_DOUBLE_BUFFER    false   // 禁用双缓冲
#define GIF_LVGL_TIMER_PERIOD_MS    500     // 2FPS
```

## 配置建议

### 稳定性优先配置 (推荐)
```cpp
#define GIF_CONSERVATIVE_MODE        1
#define GIF_LVGL_TIMER_PERIOD_MS    200     // 5FPS
#define GIF_BUFFER_DIVISOR          16      // 小缓冲区
#define GIF_ENABLE_DOUBLE_BUFFER    false   // 单缓冲
#define GIF_USE_PSRAM_BUFFER        true    // 使用PSRAM
```

### 性能优先配置 (需要足够的系统资源)
```cpp
#define GIF_CONSERVATIVE_MODE        0
#define GIF_LVGL_TIMER_PERIOD_MS    50      // 20FPS
#define GIF_BUFFER_DIVISOR          8       // 大缓冲区
#define GIF_ENABLE_DOUBLE_BUFFER    true    // 双缓冲
#define GIF_USE_PSRAM_BUFFER        true    // 使用PSRAM
```

### 内存优先配置
```cpp
#define GIF_OPTIMIZE_FOR_MEMORY      1
#define GIF_BUFFER_DIVISOR          20      // 最小缓冲区
#define GIF_USE_PSRAM_BUFFER        false   // 使用内部RAM
#define GIF_USE_DMA_BUFFER          true    // 启用DMA
```

## 调试步骤

### 1. 启用调试日志
```cpp
#define GIF_DEBUG_MEMORY_USAGE      1
#define GIF_DEBUG_SYNC_MESSAGES     1
#define GIF_DEBUG_PERFORMANCE       1
```

### 2. 监控系统状态
```cpp
// 添加到主循环中
ESP_LOGI(TAG, "Free heap: %lu", esp_get_free_heap_size());
ESP_LOGI(TAG, "Min free heap: %lu", esp_get_minimum_free_heap_size());
```

### 3. 检查任务状态
```cpp
// 打印任务列表
char pcWriteBuffer[1024];
vTaskList(pcWriteBuffer);
printf("Task List:\n%s\n", pcWriteBuffer);
```

## 渐进式优化策略

1. **第一步**: 启用保守模式，确保系统稳定运行
2. **第二步**: 逐步提高刷新率 (200ms → 100ms → 50ms)
3. **第三步**: 增大缓冲区 (1/16 → 1/10 → 1/8)
4. **第四步**: 启用双缓冲
5. **第五步**: 调整任务优先级

每一步都要确保系统稳定运行，没有看门狗超时。

## 紧急恢复

如果系统完全无法启动，使用最小配置：

```cpp
#define GIF_LVGL_TIMER_PERIOD_MS    1000    // 1FPS
#define GIF_BUFFER_DIVISOR          32      // 极小缓冲区
#define GIF_ENABLE_DOUBLE_BUFFER    false
#define GIF_SYNC_TASK_PRIORITY      1       // 最低优先级
#define GIF_LVGL_TASK_PRIORITY      1       // 最低优先级
```
