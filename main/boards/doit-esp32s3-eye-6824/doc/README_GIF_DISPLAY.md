# EyeGifDisplay - 魔眼GIF表情显示系统

## 概述

`EyeGifDisplay` 是为 `doit-esp32s3-eye-6824` 开发板专门设计的 GIF 表情显示类，继承自 `SpiLcdDisplay`，专注于全屏 GIF 动画显示。

## 功能特性

- 🎭 **纯 GIF 显示**: 专注于 GIF 动画，不显示任何文字标签
- 🎬 **全屏播放**: 240x240 全屏显示，完美适配圆形屏幕
- 🚀 **高性能**: 基于 LVGL 的 GIF 组件，支持 PSRAM 加速
- 🎨 **简洁设计**: 继承重写方式，不修改基类业务逻辑
- 🔧 **易于扩展**: 简单的映射表结构，方便添加新表情

## 支持的表情

### 实际可用的GIF资源
基于 src 目录下的实际文件：

1. **staticstate** - 静态/中性表情
   - 映射表情: `neutral`, `relaxed`, `sleepy`, `listening`

2. **happy** - 开心表情
   - 映射表情: `happy`, `laughing`, `funny`, `loving`, `confident`, `winking`, `cool`, `delicious`, `kissy`, `silly`, `speaking`

3. **sad** - 悲伤表情
   - 映射表情: `sad`, `crying`

4. **anger** - 愤怒表情
   - 映射表情: `angry`, `error`

5. **scare** - 惊吓/惊讶表情
   - 映射表情: `surprised`, `shocked`

6. **buxue** - 不学/困惑/思考表情
   - 映射表情: `thinking`, `confused`, `embarrassed`, `loading`

## 使用方法

### 1. 设置表情（主要功能）
```cpp
auto display = GetDisplay();
display->SetEmotion("happy");     // 设置开心表情，全屏显示
display->SetEmotion("thinking");  // 设置思考表情
display->SetEmotion("neutral");   // 设置中性表情
```

### 2. 系统图标映射（自动转换为表情）
```cpp
display->SetIcon(FONT_AWESOME_MUSIC);       // 自动映射到 "listening" 表情
display->SetIcon(FONT_AWESOME_VOLUME_HIGH); // 自动映射到 "speaking" 表情
display->SetIcon(FONT_AWESOME_DOWNLOAD);    // 自动映射到 "loading" 表情
```

### 3. 聊天消息（空实现）
```cpp
display->SetChatMessage("user", "消息内容");  // 不会显示任何文字，只保持当前表情
```

> **注意**: `EyeGifDisplay` 专注于纯 GIF 显示，`SetChatMessage` 为空实现，不会显示任何文字。

## GIF 资源准备

### 1. GIF 文件要求
- **尺寸**: 建议 160x160 像素（匹配屏幕尺寸）
- **格式**: GIF 动画文件
- **颜色**: RGB565 格式
- **大小**: 建议每个文件小于 100KB

### 2. 转换为 C 数组

#### 方法一：使用在线工具
1. 访问 [LVGL 图片转换器](https://lvgl.io/tools/imageconverter)
2. 上传 GIF 文件
3. 设置输出格式为 "C array"
4. 颜色格式选择 "RGB565"
5. 下载生成的 C 文件

#### 方法二：使用 LVGL 脚本
```bash
# 安装 LVGL 图片转换工具
pip install lv_img_conv

# 转换 GIF 文件
lv_img_conv.py your_gif.gif -f true_color_alpha -cf RGB565 -ff C
```

### 3. 集成到项目

1. 将转换后的 C 文件放入 `src/` 目录
2. 更新 `eye_gif_resources.h` 中的声明
3. 更新 `eye_gifs.c` 中的资源定义
4. 在 `eye_gif_display.cc` 中更新映射表

## 文件结构

```
main/boards/doit-esp32s3-eye-6824/
├── eye_gif_display.h          # GIF显示类头文件
├── eye_gif_display.cc         # GIF显示类实现
├── eye_gif_resources.h        # GIF资源声明
├── src/
│   ├── staticstate.c         # 静态/中性表情GIF数据
│   ├── happy.c               # 开心表情GIF数据
│   ├── sad.c                 # 悲伤表情GIF数据
│   ├── anger.c               # 愤怒表情GIF数据
│   ├── scare.c               # 惊吓表情GIF数据
│   ├── buxue.c               # 困惑/思考表情GIF数据
│   └── otto_emoji_gif_utils.c # GIF工具函数
└── README_GIF_DISPLAY.md     # 本说明文件
```

## 自定义表情

### 添加新表情

1. **准备 GIF 文件**
   ```bash
   # 转换新的 GIF 文件
   lv_img_conv.py my_new_emotion.gif -f true_color_alpha -cf RGB565 -ff C
   ```

2. **更新资源声明**
   ```cpp
   // 在 eye_gif_resources.h 中添加
   LV_IMG_DECLARE(eye_my_new_emotion);
   ```

3. **更新映射表**
   ```cpp
   // 在 eye_gif_display.cc 中添加
   {"my_new_emotion", &eye_my_new_emotion},
   ```

## PSRAM 内存优化

本实现已经配置为充分利用 ESP32-S3 的 PSRAM：

### 🚀 **PSRAM 配置**
- ✅ **PSRAM 已启用**: `CONFIG_SPIRAM=y`
- ✅ **使用 malloc 分配**: `CONFIG_SPIRAM_USE_MALLOC=y`
- ✅ **GIF 缓存启用**: `CONFIG_LV_GIF_CACHE_DECODE_DATA=y`
- ✅ **LVGL 使用 C 库 malloc**: `CONFIG_LV_USE_CLIB_MALLOC=y`

### 📊 **内存使用监控**
代码中集成了内存使用情况监控，会在以下时机记录内存状态：
- GIF 容器初始化时
- 设置表情前后

### 💡 **内存优化建议**

1. **内存优化**
   - GIF 数据自动使用 PSRAM 存储
   - 解码缓存启用，提高播放性能
   - 控制 GIF 文件大小，建议单个文件 < 500KB

2. **显示优化**
   - 合理设置 GIF 帧率，避免过度消耗 CPU
   - 使用适当的颜色深度 (RGB565)
   - 启用双缓冲提高流畅度

3. **存储优化**
   - 大型 GIF 资源自动使用 PSRAM
   - 支持动态加载和缓存机制
   - 内存不足时自动清理缓存

## 故障排除

### 常见问题

1. **GIF 不显示**
   - 检查 GIF 资源是否正确转换
   - 确认内存是否足够
   - 验证文件路径是否正确

2. **显示异常**
   - 检查 GIF 尺寸是否匹配屏幕
   - 确认颜色格式是否正确

3. **性能问题**
   - 减少 GIF 文件大小
   - 降低动画帧率
   - 优化内存使用

## 示例代码

```cpp
// 创建 GIF 显示实例
auto display = new EyeGifDisplay(lcd_io, lcd_panel,
    160, 160, 0, 0, false, false, false, fonts);

// 设置不同表情（使用实际可用的表情）
display->SetEmotion("happy");     // 开心 -> happy.c
display->SetEmotion("thinking");  // 思考 -> buxue.c
display->SetEmotion("sad");       // 悲伤 -> sad.c
display->SetEmotion("angry");     // 愤怒 -> anger.c
display->SetEmotion("surprised"); // 惊讶 -> scare.c
display->SetEmotion("neutral");   // 中性 -> staticstate.c

// 显示消息
display->SetChatMessage("assistant", "我正在思考...");

// 显示系统状态
display->SetIcon(FONT_AWESOME_WIFI);        // 网络状态
display->SetIcon(FONT_AWESOME_MICROPHONE);  // 听取状态 -> staticstate
display->SetIcon(FONT_AWESOME_VOLUME_UP);   // 说话状态 -> happy
```

## 注意事项

1. 确保有足够的内存来存储 GIF 数据
2. GIF 文件大小直接影响编译后的固件大小
3. 复杂的 GIF 动画可能影响系统性能
4. 建议在实际硬件上测试性能表现
