#pragma once

/**
 * 内存配置测试工具
 * 用于验证LVGL内存分配配置是否正确工作
 */
class MemoryTest {
public:
    /**
     * 运行所有内存测试
     */
    static void RunAllTests();

    /**
     * 测试基本内存分配行为
     */
    static void TestBasicAllocation();

    /**
     * 测试LVGL内存分配器
     */
    static void TestLVGLAllocation();

    /**
     * 测试大内存分配
     */
    static void TestLargeAllocation();

    /**
     * 测试不同内存池的分配
     */
    static void TestMemoryPools();

    /**
     * 测试内存泄漏检测
     */
    static void TestMemoryLeakDetection();

    /**
     * 打印当前内存配置信息
     */
    static void PrintCurrentConfiguration();
};
