#pragma once

#include "lvgl.h"
#include "esp_heap_caps.h"
#include "esp_log.h"
#include "esp_timer.h"
#include <vector>
#include <unordered_map>
#include <string>
#include <memory>

/**
 * GIF解码缓存器
 * 预解码GIF帧到PSRAM中，提供高性能播放
 */
class GifDecoderCache {
public:
    /**
     * 解码后的帧数据结构
     */
    struct DecodedFrame {
        uint16_t* pixel_data;       // RGB565像素数据
        uint32_t width;             // 帧宽度
        uint32_t height;            // 帧高度
        uint32_t delay_ms;          // 帧延迟时间
        uint32_t frame_index;       // 帧索引
        size_t data_size;           // 数据大小
        bool is_psram;              // 是否存储在PSRAM
        uint32_t decode_time_us;    // 解码耗时(微秒)
    };

    /**
     * GIF缓存条目
     */
    struct GifCacheEntry {
        std::vector<DecodedFrame> frames;       // 解码后的帧
        uint32_t total_frames;                  // 总帧数
        uint32_t cached_frames;                 // 已缓存帧数
        uint32_t current_frame;                 // 当前播放帧
        uint32_t width;                         // GIF宽度
        uint32_t height;                        // GIF高度
        size_t total_memory;                    // 总内存使用
        uint32_t last_access_time;              // 最后访问时间
        std::string emotion_name;               // 表情名称
        const lv_img_dsc_t* original_gif;       // 原始GIF数据
        bool is_fully_cached;                   // 是否完全缓存
        bool is_decoding;                       // 是否正在解码
    };

    /**
     * 解码配置
     */
    struct DecodeConfig {
        uint32_t max_cached_gifs = 5;           // 最大缓存GIF数量
        uint32_t frames_per_gif = 10;           // 每个GIF缓存帧数
        bool use_psram = true;                  // 使用PSRAM
        bool async_decode = true;               // 异步解码
        uint32_t decode_task_priority = 3;      // 解码任务优先级
        uint32_t decode_task_stack = 8192;      // 解码任务堆栈
        uint32_t max_decode_time_ms = 100;      // 最大解码时间
    };

    /**
     * 初始化GIF解码缓存器
     */
    static void Initialize(const DecodeConfig& config = DecodeConfig{});

    /**
     * 预解码GIF帧
     * @param gif_dsc GIF描述符
     * @param emotion_name 表情名称
     * @param priority 解码优先级 (0=最高)
     * @return 是否成功启动解码
     */
    static bool StartDecodeGif(const lv_img_dsc_t* gif_dsc, 
                              const char* emotion_name, 
                              uint32_t priority = 1);

    /**
     * 获取解码后的帧
     * @param emotion_name 表情名称
     * @param frame_index 帧索引
     * @return 解码后的帧数据，如果不存在返回nullptr
     */
    static const DecodedFrame* GetDecodedFrame(const char* emotion_name, uint32_t frame_index);

    /**
     * 获取下一帧（自动循环）
     * @param emotion_name 表情名称
     * @return 下一帧数据
     */
    static const DecodedFrame* GetNextFrame(const char* emotion_name);

    /**
     * 检查GIF是否已完全解码
     */
    static bool IsFullyDecoded(const char* emotion_name);

    /**
     * 获取解码进度 (0-100)
     */
    static uint32_t GetDecodeProgress(const char* emotion_name);

    /**
     * 设置当前播放帧
     */
    static void SetCurrentFrame(const char* emotion_name, uint32_t frame_index);

    /**
     * 清理指定GIF缓存
     */
    static void ClearGifCache(const char* emotion_name);

    /**
     * 清理所有缓存
     */
    static void ClearAllCache();

    /**
     * 强制垃圾回收
     */
    static void ForceGarbageCollection();

    /**
     * 打印缓存统计信息
     */
    static void PrintCacheStats();

    /**
     * 获取内存使用统计
     */
    static size_t GetTotalMemoryUsage();

    /**
     * 检查内存健康状况
     */
    static bool CheckMemoryHealth();

    /**
     * 停止所有解码任务
     */
    static void StopAllDecoding();

private:
    static const char* TAG;
    static bool initialized_;
    static DecodeConfig config_;
    static std::unordered_map<std::string, std::shared_ptr<GifCacheEntry>> cache_;
    static TaskHandle_t decode_task_handle_;
    static QueueHandle_t decode_queue_;
    static bool decode_task_running_;

    /**
     * 解码任务消息
     */
    struct DecodeMessage {
        std::string emotion_name;
        const lv_img_dsc_t* gif_dsc;
        uint32_t priority;
        uint32_t start_frame;
        uint32_t frame_count;
    };

    /**
     * 统计信息
     */
    struct CacheStats {
        size_t total_decoded_frames;
        size_t total_memory_used;
        size_t decode_requests;
        size_t decode_successes;
        size_t decode_failures;
        size_t cache_hits;
        size_t cache_misses;
        uint32_t avg_decode_time_us;
        uint32_t max_decode_time_us;
    };
    static CacheStats stats_;

    /**
     * 解码任务主函数
     */
    static void DecodeTask(void* parameter);

    /**
     * 解码单个GIF帧
     */
    static bool DecodeSingleFrame(const lv_img_dsc_t* gif_dsc, 
                                 uint32_t frame_index, 
                                 DecodedFrame& decoded_frame);

    /**
     * 分配帧内存
     */
    static uint16_t* AllocateFrameMemory(uint32_t width, uint32_t height);

    /**
     * 释放帧内存
     */
    static void FreeFrameMemory(uint16_t* pixel_data);

    /**
     * 清理最旧的缓存
     */
    static void EvictOldestCache();

    /**
     * 检查是否需要清理缓存
     */
    static bool NeedsCacheEviction();

    /**
     * 获取GIF信息（宽度、高度、帧数）
     */
    static bool GetGifInfo(const lv_img_dsc_t* gif_dsc, 
                          uint32_t& width, 
                          uint32_t& height, 
                          uint32_t& frame_count);

    /**
     * 验证GIF数据完整性
     */
    static bool ValidateGifData(const lv_img_dsc_t* gif_dsc);
};
