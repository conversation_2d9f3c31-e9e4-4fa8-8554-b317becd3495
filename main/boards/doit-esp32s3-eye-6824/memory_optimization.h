#pragma once

#include "esp_heap_caps.h"
#include "esp_log.h"

/**
 * 内存优化工具
 * 提供针对ESP32S3 + PSRAM的内存优化策略
 */
class MemoryOptimization {
public:
    /**
     * 初始化内存优化
     */
    static void Initialize();

    /**
     * 智能内存分配
     * 根据大小和用途选择最佳内存类型
     */
    static void* SmartMalloc(size_t size, const char* purpose = nullptr);

    /**
     * 智能内存释放
     */
    static void SmartFree(void* ptr);

    /**
     * 为LVGL分配显示缓冲区
     * 强制使用PSRAM以节省内部RAM
     */
    static void* AllocateDisplayBuffer(size_t size);

    /**
     * 释放显示缓冲区
     */
    static void FreeDisplayBuffer(void* buffer);

    /**
     * 打印内存优化统计信息
     */
    static void PrintStats();

    /**
     * 检查内存健康状况
     */
    static bool CheckMemoryHealth();

    /**
     * 获取推荐的缓冲区配置
     */
    static void GetRecommendedBufferConfig(uint32_t screen_width, 
                                         uint32_t screen_height,
                                         uint32_t* buffer_size,
                                         bool* enable_double_buffer,
                                         bool* use_psram);

private:
    static const char* TAG;
    static bool initialized_;
    
    // 统计信息
    struct Stats {
        size_t smart_alloc_count;
        size_t smart_alloc_bytes;
        size_t display_buffer_count;
        size_t display_buffer_bytes;
    };
    
    static Stats stats_;
};
