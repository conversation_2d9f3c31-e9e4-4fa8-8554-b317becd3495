#pragma once

#include "esp_heap_caps.h"
#include <cstdlib>

/**
 * PSRAM优先内存分配器
 * 通过重定义malloc/free函数来优先使用PSRAM
 */

#ifdef __cplusplus
extern "C" {
#endif

/**
 * PSRAM优先的malloc实现
 * 优先尝试PSRAM分配，失败时回退到内部RAM
 */
void* psram_malloc(size_t size);

/**
 * PSRAM优先的calloc实现
 */
void* psram_calloc(size_t num, size_t size);

/**
 * PSRAM优先的realloc实现
 */
void* psram_realloc(void* ptr, size_t size);

/**
 * 智能free实现
 * 自动检测内存类型并释放
 */
void psram_free(void* ptr);

/**
 * 获取内存分配统计信息
 */
void psram_malloc_stats();

/**
 * 初始化PSRAM内存分配器
 */
void psram_malloc_init();

#ifdef __cplusplus
}
#endif

// 可选：重定义标准malloc函数（需要谨慎使用）
#ifdef ENABLE_PSRAM_MALLOC_OVERRIDE
#define malloc(size) psram_malloc(size)
#define calloc(num, size) psram_calloc(num, size)
#define realloc(ptr, size) psram_realloc(ptr, size)
#define free(ptr) psram_free(ptr)
#endif
