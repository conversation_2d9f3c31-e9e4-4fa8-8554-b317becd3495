#include "memory_monitor.h"
#include "psram_malloc.h"
#include <cstring>

const char* MemoryMonitor::TAG = "MemoryMonitor";
MemoryMonitor::MemorySnapshot MemoryMonitor::baseline_snapshot_ = {};
TaskHandle_t MemoryMonitor::monitor_task_handle_ = nullptr;
bool MemoryMonitor::monitoring_enabled_ = false;

void MemoryMonitor::Initialize() {
    ESP_LOGI(TAG, "Initializing Memory Monitor");

    // 初始化PSRAM优先分配器
    psram_malloc_init();

    baseline_snapshot_ = TakeSnapshot();
    ESP_LOGI(TAG, "Baseline snapshot taken");
}

MemoryMonitor::MemorySnapshot MemoryMonitor::TakeSnapshot() {
    MemorySnapshot snapshot;
    snapshot.free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    snapshot.free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    snapshot.free_dma = heap_caps_get_free_size(MALLOC_CAP_DMA);
    snapshot.min_free_internal = heap_caps_get_minimum_free_size(MALLOC_CAP_INTERNAL);
    snapshot.min_free_psram = heap_caps_get_minimum_free_size(MALLOC_CAP_SPIRAM);
    snapshot.timestamp = esp_timer_get_time() / 1000; // ms
    return snapshot;
}

void MemoryMonitor::CompareSnapshots(const MemorySnapshot& before, 
                                   const MemorySnapshot& after, 
                                   const char* operation_name) {
    ESP_LOGI(TAG, "=== Memory Comparison: %s ===", operation_name);
    
    int32_t internal_diff = (int32_t)after.free_internal - (int32_t)before.free_internal;
    int32_t psram_diff = (int32_t)after.free_psram - (int32_t)before.free_psram;
    int32_t dma_diff = (int32_t)after.free_dma - (int32_t)before.free_dma;
    
    ESP_LOGI(TAG, "Internal RAM: %zu -> %zu (%+ld bytes)",
             before.free_internal, after.free_internal, (long)internal_diff);
    ESP_LOGI(TAG, "PSRAM: %zu -> %zu (%+ld bytes)",
             before.free_psram, after.free_psram, (long)psram_diff);
    ESP_LOGI(TAG, "DMA: %zu -> %zu (%+ld bytes)",
             before.free_dma, after.free_dma, (long)dma_diff);

    if (internal_diff < -1000) {
        ESP_LOGW(TAG, "⚠️  Significant internal RAM decrease: %ld bytes", (long)(-internal_diff));
    }
    if (psram_diff < -1000) {
        ESP_LOGW(TAG, "⚠️  Significant PSRAM decrease: %ld bytes", (long)(-psram_diff));
    }
}

void MemoryMonitor::PrintDetailedMemoryInfo() {
    ESP_LOGI(TAG, "=== Detailed Memory Information ===");
    
    // 基本内存信息
    size_t free_heap = heap_caps_get_free_size(MALLOC_CAP_DEFAULT);
    size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    size_t free_dma = heap_caps_get_free_size(MALLOC_CAP_DMA);
    size_t free_8bit = heap_caps_get_free_size(MALLOC_CAP_8BIT);
    size_t free_32bit = heap_caps_get_free_size(MALLOC_CAP_32BIT);
    
    ESP_LOGI(TAG, "Free memory pools:");
    ESP_LOGI(TAG, "  Default heap: %zu bytes", free_heap);
    ESP_LOGI(TAG, "  Internal RAM: %zu bytes", free_internal);
    ESP_LOGI(TAG, "  PSRAM: %zu bytes", free_psram);
    ESP_LOGI(TAG, "  DMA capable: %zu bytes", free_dma);
    ESP_LOGI(TAG, "  8-bit access: %zu bytes", free_8bit);
    ESP_LOGI(TAG, "  32-bit access: %zu bytes", free_32bit);
    
    // 最小可用内存
    size_t min_free_internal = heap_caps_get_minimum_free_size(MALLOC_CAP_INTERNAL);
    size_t min_free_psram = heap_caps_get_minimum_free_size(MALLOC_CAP_SPIRAM);
    ESP_LOGI(TAG, "Minimum free memory:");
    ESP_LOGI(TAG, "  Internal RAM: %zu bytes", min_free_internal);
    ESP_LOGI(TAG, "  PSRAM: %zu bytes", min_free_psram);
    
    // 计算使用率
    size_t total_internal = 320 * 1024; // ESP32S3大约320KB内部RAM
    size_t total_psram = 8 * 1024 * 1024; // 8MB PSRAM
    
    float internal_usage = (float)(total_internal - free_internal) / total_internal * 100;
    float psram_usage = (float)(total_psram - free_psram) / total_psram * 100;
    
    ESP_LOGI(TAG, "Memory usage:");
    ESP_LOGI(TAG, "  Internal RAM: %.1f%% used", internal_usage);
    ESP_LOGI(TAG, "  PSRAM: %.1f%% used", psram_usage);
}

bool MemoryMonitor::DetectMemoryLeak(size_t threshold_bytes) {
    MemorySnapshot current = TakeSnapshot();
    
    int32_t internal_diff = (int32_t)baseline_snapshot_.free_internal - (int32_t)current.free_internal;
    int32_t psram_diff = (int32_t)baseline_snapshot_.free_psram - (int32_t)current.free_psram;
    
    bool leak_detected = false;
    
    if (internal_diff > (int32_t)threshold_bytes) {
        ESP_LOGW(TAG, "🚨 Internal RAM leak detected: %ld bytes lost since baseline", (long)internal_diff);
        leak_detected = true;
    }

    if (psram_diff > (int32_t)threshold_bytes) {
        ESP_LOGW(TAG, "🚨 PSRAM leak detected: %ld bytes lost since baseline", (long)psram_diff);
        leak_detected = true;
    }
    
    return leak_detected;
}

void MemoryMonitor::TestMemoryAllocation() {
    ESP_LOGI(TAG, "=== Testing Memory Allocation Behavior ===");

    // 测试标准malloc
    void* std_small = malloc(1024);
    ESP_LOGI(TAG, "Standard malloc(1KB): %p", std_small);

    void* std_large = malloc(64 * 1024);
    ESP_LOGI(TAG, "Standard malloc(64KB): %p", std_large);

    // 测试PSRAM优先分配器
    void* psram_small = psram_malloc(1024);
    ESP_LOGI(TAG, "PSRAM malloc(1KB): %p", psram_small);

    void* psram_large = psram_malloc(64 * 1024);
    ESP_LOGI(TAG, "PSRAM malloc(64KB): %p", psram_large);

    // 测试强制分配
    void* force_psram = heap_caps_malloc(1024, MALLOC_CAP_SPIRAM);
    ESP_LOGI(TAG, "Force PSRAM(1KB): %p", force_psram);

    void* force_internal = heap_caps_malloc(1024, MALLOC_CAP_INTERNAL);
    ESP_LOGI(TAG, "Force Internal(1KB): %p", force_internal);

    // 清理
    if (std_small) free(std_small);
    if (std_large) free(std_large);
    if (psram_small) psram_free(psram_small);
    if (psram_large) psram_free(psram_large);
    if (force_psram) heap_caps_free(force_psram);
    if (force_internal) heap_caps_free(force_internal);

    // 打印统计信息
    psram_malloc_stats();
}

void MemoryMonitor::StartMonitoringTask(uint32_t monitor_interval_ms) {
    if (monitor_task_handle_ != nullptr) {
        ESP_LOGW(TAG, "Monitoring task already running");
        return;
    }
    
    monitoring_enabled_ = true;
    
    xTaskCreatePinnedToCore(
        MonitoringTask,
        "memory_monitor",
        4096,
        &monitor_interval_ms,
        1,
        &monitor_task_handle_,
        0
    );
    
    ESP_LOGI(TAG, "Memory monitoring task started (interval: %lu ms)", monitor_interval_ms);
}

void MemoryMonitor::StopMonitoringTask() {
    if (monitor_task_handle_ != nullptr) {
        monitoring_enabled_ = false;
        vTaskDelete(monitor_task_handle_);
        monitor_task_handle_ = nullptr;
        ESP_LOGI(TAG, "Memory monitoring task stopped");
    }
}

void MemoryMonitor::MonitoringTask(void* parameter) {
    uint32_t interval_ms = *(uint32_t*)parameter;
    MemorySnapshot last_snapshot = TakeSnapshot();
    
    while (monitoring_enabled_) {
        vTaskDelay(pdMS_TO_TICKS(interval_ms));
        
        MemorySnapshot current_snapshot = TakeSnapshot();
        
        // 检查显著变化
        int32_t internal_diff = (int32_t)current_snapshot.free_internal - (int32_t)last_snapshot.free_internal;
        int32_t psram_diff = (int32_t)current_snapshot.free_psram - (int32_t)last_snapshot.free_psram;
        
        if (abs(internal_diff) > 1000 || abs(psram_diff) > 1000) {
            ESP_LOGI(TAG, "Memory change detected:");
            ESP_LOGI(TAG, "  Internal: %+ld bytes, PSRAM: %+ld bytes", (long)internal_diff, (long)psram_diff);
        }
        
        // 检查内存泄漏
        if (DetectMemoryLeak(5000)) { // 5KB阈值
            PrintDetailedMemoryInfo();
        }
        
        last_snapshot = current_snapshot;
    }
    
    vTaskDelete(nullptr);
}
