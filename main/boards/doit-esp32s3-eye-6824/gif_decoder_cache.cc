#include "gif_decoder_cache.h"
#include "psram_force_allocator.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include <cstring>
#include <algorithm>

const char* GifDecoderCache::TAG = "GifDecoderCache";
bool GifDecoderCache::initialized_ = false;
GifDecoderCache::DecodeConfig GifDecoderCache::config_;
std::unordered_map<std::string, std::shared_ptr<GifDecoderCache::GifCacheEntry>> GifDecoderCache::cache_;
TaskHandle_t GifDecoderCache::decode_task_handle_ = nullptr;
QueueHandle_t GifDecoderCache::decode_queue_ = nullptr;
bool GifDecoderCache::decode_task_running_ = false;
GifDecoderCache::CacheStats GifDecoderCache::stats_ = {0};

void GifDecoderCache::Initialize(const DecodeConfig& config) {
    if (initialized_) {
        return;
    }

    ESP_LOGI(TAG, "Initializing GIF Decoder Cache");
    
    config_ = config;
    
    // 检查内存可用性
    size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    
    ESP_LOGI(TAG, "Memory available:");
    ESP_LOGI(TAG, "  PSRAM: %zu bytes (%.1f MB)", free_psram, free_psram / (1024.0 * 1024.0));
    ESP_LOGI(TAG, "  Internal: %zu bytes (%.1f KB)", free_internal, free_internal / 1024.0);
    
    // 根据可用内存调整配置
    if (free_psram < 2 * 1024 * 1024) { // 少于2MB PSRAM
        ESP_LOGW(TAG, "Limited PSRAM, reducing cache configuration");
        config_.max_cached_gifs = 3;
        config_.frames_per_gif = 5;
    }
    
    // 创建解码队列
    decode_queue_ = xQueueCreate(10, sizeof(DecodeMessage));
    if (!decode_queue_) {
        ESP_LOGE(TAG, "Failed to create decode queue");
        return;
    }
    
    // 创建解码任务
    decode_task_running_ = true;
    BaseType_t result = xTaskCreatePinnedToCore(
        DecodeTask,
        "gif_decoder",
        config_.decode_task_stack,
        nullptr,
        config_.decode_task_priority,
        &decode_task_handle_,
        1  // 绑定到核心1，避免与LVGL冲突
    );
    
    if (result != pdPASS) {
        ESP_LOGE(TAG, "Failed to create decode task");
        vQueueDelete(decode_queue_);
        decode_queue_ = nullptr;
        return;
    }
    
    // 清空缓存和统计
    cache_.clear();
    memset(&stats_, 0, sizeof(stats_));
    
    initialized_ = true;
    ESP_LOGI(TAG, "GIF Decoder Cache initialized successfully");
    ESP_LOGI(TAG, "Config: max_gifs=%lu, frames_per_gif=%lu, use_psram=%s, async=%s",
             config_.max_cached_gifs, config_.frames_per_gif,
             config_.use_psram ? "yes" : "no",
             config_.async_decode ? "yes" : "no");
}

bool GifDecoderCache::StartDecodeGif(const lv_img_dsc_t* gif_dsc, 
                                    const char* emotion_name, 
                                    uint32_t priority) {
    if (!initialized_ || !gif_dsc || !emotion_name) {
        ESP_LOGE(TAG, "Invalid parameters for StartDecodeGif");
        return false;
    }
    
    std::string emotion_key(emotion_name);
    
    // 检查是否已经在缓存中
    auto it = cache_.find(emotion_key);
    if (it != cache_.end()) {
        ESP_LOGD(TAG, "GIF '%s' already in cache", emotion_name);
        it->second->last_access_time = esp_timer_get_time() / 1000;
        return true;
    }
    
    // 验证GIF数据
    if (!ValidateGifData(gif_dsc)) {
        ESP_LOGE(TAG, "Invalid GIF data for '%s'", emotion_name);
        return false;
    }
    
    // 检查是否需要清理缓存
    if (NeedsCacheEviction()) {
        EvictOldestCache();
    }
    
    ESP_LOGI(TAG, "Starting decode for GIF '%s' (priority: %lu)", emotion_name, priority);
    
    // 创建缓存条目
    auto cache_entry = std::make_shared<GifCacheEntry>();
    cache_entry->emotion_name = emotion_name;
    cache_entry->original_gif = gif_dsc;
    cache_entry->last_access_time = esp_timer_get_time() / 1000;
    cache_entry->is_fully_cached = false;
    cache_entry->is_decoding = true;
    cache_entry->current_frame = 0;
    cache_entry->cached_frames = 0;
    cache_entry->total_memory = 0;
    
    // 获取GIF信息
    if (!GetGifInfo(gif_dsc, cache_entry->width, cache_entry->height, cache_entry->total_frames)) {
        ESP_LOGE(TAG, "Failed to get GIF info for '%s'", emotion_name);
        return false;
    }
    
    ESP_LOGI(TAG, "GIF '%s' info: %lux%lu, %lu frames", 
             emotion_name, cache_entry->width, cache_entry->height, cache_entry->total_frames);
    
    // 预分配帧向量
    uint32_t frames_to_cache = std::min(config_.frames_per_gif, cache_entry->total_frames);
    cache_entry->frames.reserve(frames_to_cache);
    
    // 添加到缓存
    cache_[emotion_key] = cache_entry;
    
    // 发送解码消息
    DecodeMessage msg;
    msg.emotion_name = emotion_name;
    msg.gif_dsc = gif_dsc;
    msg.priority = priority;
    msg.start_frame = 0;
    msg.frame_count = frames_to_cache;
    
    if (xQueueSend(decode_queue_, &msg, pdMS_TO_TICKS(100)) != pdTRUE) {
        ESP_LOGE(TAG, "Failed to queue decode message for '%s'", emotion_name);
        cache_.erase(emotion_key);
        return false;
    }
    
    stats_.decode_requests++;
    return true;
}

const GifDecoderCache::DecodedFrame* GifDecoderCache::GetDecodedFrame(const char* emotion_name, 
                                                                     uint32_t frame_index) {
    if (!initialized_ || !emotion_name) {
        stats_.cache_misses++;
        return nullptr;
    }
    
    std::string emotion_key(emotion_name);
    auto it = cache_.find(emotion_key);
    
    if (it == cache_.end()) {
        stats_.cache_misses++;
        return nullptr;
    }
    
    auto& cache_entry = it->second;
    cache_entry->last_access_time = esp_timer_get_time() / 1000;
    
    // 检查帧是否可用
    if (frame_index >= cache_entry->frames.size()) {
        // 如果请求的帧超出缓存范围，返回最后一帧或循环到开始
        if (cache_entry->frames.empty()) {
            stats_.cache_misses++;
            return nullptr;
        }
        frame_index = frame_index % cache_entry->frames.size();
    }
    
    stats_.cache_hits++;
    return &cache_entry->frames[frame_index];
}

const GifDecoderCache::DecodedFrame* GifDecoderCache::GetNextFrame(const char* emotion_name) {
    if (!initialized_ || !emotion_name) {
        return nullptr;
    }
    
    std::string emotion_key(emotion_name);
    auto it = cache_.find(emotion_key);
    
    if (it == cache_.end()) {
        return nullptr;
    }
    
    auto& cache_entry = it->second;
    
    if (cache_entry->frames.empty()) {
        return nullptr;
    }
    
    // 获取当前帧
    const DecodedFrame* current_frame = &cache_entry->frames[cache_entry->current_frame];
    
    // 移动到下一帧
    cache_entry->current_frame = (cache_entry->current_frame + 1) % cache_entry->frames.size();
    cache_entry->last_access_time = esp_timer_get_time() / 1000;
    
    stats_.cache_hits++;
    return current_frame;
}

bool GifDecoderCache::IsFullyDecoded(const char* emotion_name) {
    if (!initialized_ || !emotion_name) {
        return false;
    }
    
    std::string emotion_key(emotion_name);
    auto it = cache_.find(emotion_key);
    
    return (it != cache_.end()) ? it->second->is_fully_cached : false;
}

uint32_t GifDecoderCache::GetDecodeProgress(const char* emotion_name) {
    if (!initialized_ || !emotion_name) {
        return 0;
    }
    
    std::string emotion_key(emotion_name);
    auto it = cache_.find(emotion_key);
    
    if (it == cache_.end()) {
        return 0;
    }
    
    auto& cache_entry = it->second;
    uint32_t target_frames = std::min(config_.frames_per_gif, cache_entry->total_frames);
    
    if (target_frames == 0) {
        return 0;
    }
    
    return (cache_entry->cached_frames * 100) / target_frames;
}

void GifDecoderCache::SetCurrentFrame(const char* emotion_name, uint32_t frame_index) {
    if (!initialized_ || !emotion_name) {
        return;
    }
    
    std::string emotion_key(emotion_name);
    auto it = cache_.find(emotion_key);
    
    if (it != cache_.end() && !it->second->frames.empty()) {
        it->second->current_frame = frame_index % it->second->frames.size();
        it->second->last_access_time = esp_timer_get_time() / 1000;
    }
}

uint16_t* GifDecoderCache::AllocateFrameMemory(uint32_t width, uint32_t height) {
    size_t size = width * height * sizeof(uint16_t); // RGB565
    
    if (config_.use_psram) {
        return (uint16_t*)PSRAMForceAllocator::Malloc(size);
    } else {
        return (uint16_t*)heap_caps_malloc(size, MALLOC_CAP_INTERNAL | MALLOC_CAP_8BIT);
    }
}

void GifDecoderCache::FreeFrameMemory(uint16_t* pixel_data) {
    if (pixel_data) {
        if (config_.use_psram) {
            PSRAMForceAllocator::Free(pixel_data);
        } else {
            heap_caps_free(pixel_data);
        }
    }
}

bool GifDecoderCache::ValidateGifData(const lv_img_dsc_t* gif_dsc) {
    if (!gif_dsc || !gif_dsc->data || gif_dsc->data_size < 10) {
        return false;
    }
    
    // 检查GIF文件头
    const uint8_t* data = (const uint8_t*)gif_dsc->data;
    if (data[0] != 'G' || data[1] != 'I' || data[2] != 'F') {
        return false;
    }
    
    // 检查版本
    if ((data[3] != '8' || (data[4] != '7' && data[4] != '9')) || data[5] != 'a') {
        return false;
    }
    
    return true;
}

bool GifDecoderCache::GetGifInfo(const lv_img_dsc_t* gif_dsc, 
                                uint32_t& width, 
                                uint32_t& height, 
                                uint32_t& frame_count) {
    if (!ValidateGifData(gif_dsc)) {
        return false;
    }
    
    const uint8_t* data = (const uint8_t*)gif_dsc->data;
    
    // 从GIF头部读取宽度和高度
    width = data[6] | (data[7] << 8);
    height = data[8] | (data[9] << 8);
    
    // 简单的帧数估算（实际实现需要解析整个GIF）
    // 这里使用一个保守的估算
    frame_count = std::min(20U, config_.frames_per_gif); // 假设最多20帧
    
    ESP_LOGD(TAG, "GIF info: %lux%lu, estimated %lu frames", width, height, frame_count);
    
    return width > 0 && height > 0 && width <= 320 && height <= 240;
}

void GifDecoderCache::DecodeTask(void* parameter) {
    ESP_LOGI(TAG, "GIF decode task started");

    DecodeMessage msg;

    while (decode_task_running_) {
        // 等待解码消息
        if (xQueueReceive(decode_queue_, &msg, pdMS_TO_TICKS(1000)) == pdTRUE) {
            ESP_LOGI(TAG, "Processing decode request for '%s'", msg.emotion_name.c_str());

            uint32_t decode_start_time = esp_timer_get_time();

            // 查找缓存条目
            auto it = cache_.find(msg.emotion_name);
            if (it == cache_.end()) {
                ESP_LOGW(TAG, "Cache entry not found for '%s'", msg.emotion_name.c_str());
                continue;
            }

            auto& cache_entry = it->second;
            bool decode_success = true;

            // 解码指定数量的帧
            for (uint32_t i = 0; i < msg.frame_count && decode_task_running_; i++) {
                uint32_t frame_index = msg.start_frame + i;

                DecodedFrame decoded_frame;
                decoded_frame.frame_index = frame_index;
                decoded_frame.width = cache_entry->width;
                decoded_frame.height = cache_entry->height;
                decoded_frame.delay_ms = 80; // 默认延迟

                // 分配像素内存
                decoded_frame.pixel_data = AllocateFrameMemory(cache_entry->width, cache_entry->height);
                if (!decoded_frame.pixel_data) {
                    ESP_LOGE(TAG, "Failed to allocate memory for frame %lu", frame_index);
                    decode_success = false;
                    break;
                }

                decoded_frame.data_size = cache_entry->width * cache_entry->height * sizeof(uint16_t);
                decoded_frame.is_psram = config_.use_psram;

                uint32_t frame_decode_start = esp_timer_get_time();

                // 实际解码帧（这里使用模拟数据）
                if (DecodeSingleFrame(msg.gif_dsc, frame_index, decoded_frame)) {
                    decoded_frame.decode_time_us = esp_timer_get_time() - frame_decode_start;

                    // 添加到缓存
                    cache_entry->frames.push_back(decoded_frame);
                    cache_entry->cached_frames++;
                    cache_entry->total_memory += decoded_frame.data_size;
                    stats_.total_decoded_frames++;
                    stats_.total_memory_used += decoded_frame.data_size;

                    // 更新统计
                    if (decoded_frame.decode_time_us > stats_.max_decode_time_us) {
                        stats_.max_decode_time_us = decoded_frame.decode_time_us;
                    }

                    ESP_LOGD(TAG, "Decoded frame %lu for '%s' in %lu us",
                             frame_index, msg.emotion_name.c_str(), decoded_frame.decode_time_us);
                } else {
                    ESP_LOGE(TAG, "Failed to decode frame %lu for '%s'", frame_index, msg.emotion_name.c_str());
                    FreeFrameMemory(decoded_frame.pixel_data);
                    decode_success = false;
                    break;
                }

                // 检查解码时间限制
                uint32_t elapsed_time = (esp_timer_get_time() - decode_start_time) / 1000;
                if (elapsed_time > config_.max_decode_time_ms) {
                    ESP_LOGW(TAG, "Decode time limit exceeded for '%s', stopping at frame %lu",
                             msg.emotion_name.c_str(), frame_index);
                    break;
                }

                // 让出CPU时间
                vTaskDelay(pdMS_TO_TICKS(1));
            }

            // 更新缓存状态
            cache_entry->is_decoding = false;
            cache_entry->is_fully_cached = (cache_entry->cached_frames >=
                                          std::min(config_.frames_per_gif, cache_entry->total_frames));

            if (decode_success) {
                stats_.decode_successes++;
                ESP_LOGI(TAG, "Successfully decoded %lu frames for '%s' (%.1f KB)",
                         cache_entry->cached_frames, msg.emotion_name.c_str(),
                         cache_entry->total_memory / 1024.0);
            } else {
                stats_.decode_failures++;
                ESP_LOGE(TAG, "Failed to decode '%s'", msg.emotion_name.c_str());
            }

            // 计算平均解码时间
            uint32_t total_decode_time = (esp_timer_get_time() - decode_start_time);
            if (stats_.total_decoded_frames > 0) {
                stats_.avg_decode_time_us = (stats_.avg_decode_time_us + total_decode_time) / 2;
            } else {
                stats_.avg_decode_time_us = total_decode_time;
            }
        }
    }

    ESP_LOGI(TAG, "GIF decode task stopped");
    vTaskDelete(nullptr);
}

bool GifDecoderCache::DecodeSingleFrame(const lv_img_dsc_t* gif_dsc,
                                       uint32_t frame_index,
                                       DecodedFrame& decoded_frame) {
    if (!gif_dsc || !decoded_frame.pixel_data) {
        return false;
    }

    // 这里应该实现实际的GIF帧解码
    // 目前使用模拟数据来演示缓存机制

    uint16_t* pixels = decoded_frame.pixel_data;
    uint32_t pixel_count = decoded_frame.width * decoded_frame.height;

    // 生成测试图案（不同帧使用不同颜色）
    uint16_t base_color = 0x0000; // 黑色
    switch (frame_index % 4) {
        case 0: base_color = 0xF800; break; // 红色
        case 1: base_color = 0x07E0; break; // 绿色
        case 2: base_color = 0x001F; break; // 蓝色
        case 3: base_color = 0xFFFF; break; // 白色
    }

    // 填充像素数据
    for (uint32_t i = 0; i < pixel_count; i++) {
        pixels[i] = base_color;
    }

    // 模拟解码延迟
    vTaskDelay(pdMS_TO_TICKS(5)); // 5ms解码时间

    return true;
}

void GifDecoderCache::ClearGifCache(const char* emotion_name) {
    if (!initialized_ || !emotion_name) {
        return;
    }

    std::string emotion_key(emotion_name);
    auto it = cache_.find(emotion_key);

    if (it != cache_.end()) {
        auto& cache_entry = it->second;

        // 释放所有帧内存
        for (auto& frame : cache_entry->frames) {
            FreeFrameMemory(frame.pixel_data);
        }

        stats_.total_memory_used -= cache_entry->total_memory;
        stats_.total_decoded_frames -= cache_entry->cached_frames;

        cache_.erase(it);
        ESP_LOGI(TAG, "Cleared cache for '%s'", emotion_name);
    }
}

void GifDecoderCache::ClearAllCache() {
    ESP_LOGI(TAG, "Clearing all GIF decode cache");

    for (auto& pair : cache_) {
        auto& cache_entry = pair.second;
        for (auto& frame : cache_entry->frames) {
            FreeFrameMemory(frame.pixel_data);
        }
    }

    cache_.clear();
    memset(&stats_, 0, sizeof(stats_));

    ESP_LOGI(TAG, "All decode cache cleared");
}

void GifDecoderCache::EvictOldestCache() {
    if (cache_.empty()) {
        return;
    }

    // 找到最旧的缓存
    auto oldest_it = cache_.begin();
    uint32_t oldest_time = oldest_it->second->last_access_time;

    for (auto it = cache_.begin(); it != cache_.end(); ++it) {
        if (it->second->last_access_time < oldest_time) {
            oldest_time = it->second->last_access_time;
            oldest_it = it;
        }
    }

    ESP_LOGI(TAG, "Evicting oldest cache: '%s'", oldest_it->second->emotion_name.c_str());
    ClearGifCache(oldest_it->second->emotion_name.c_str());
}

bool GifDecoderCache::NeedsCacheEviction() {
    return cache_.size() >= config_.max_cached_gifs;
}

void GifDecoderCache::StopAllDecoding() {
    ESP_LOGI(TAG, "Stopping all decoding tasks");

    decode_task_running_ = false;

    if (decode_task_handle_) {
        // 等待任务结束
        vTaskDelay(pdMS_TO_TICKS(100));
        decode_task_handle_ = nullptr;
    }

    if (decode_queue_) {
        vQueueDelete(decode_queue_);
        decode_queue_ = nullptr;
    }
}

size_t GifDecoderCache::GetTotalMemoryUsage() {
    return stats_.total_memory_used;
}

bool GifDecoderCache::CheckMemoryHealth() {
    size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    size_t free_internal = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);

    bool healthy = true;

    if (config_.use_psram && free_psram < 1024 * 1024) { // 少于1MB PSRAM
        ESP_LOGW(TAG, "Low PSRAM memory: %zu bytes", free_psram);
        healthy = false;
    }

    if (free_internal < 32 * 1024) { // 少于32KB内部RAM
        ESP_LOGW(TAG, "Low internal memory: %zu bytes", free_internal);
        healthy = false;
    }

    return healthy;
}

void GifDecoderCache::ForceGarbageCollection() {
    ESP_LOGI(TAG, "Performing decode cache garbage collection");

    // 清理一半的缓存
    size_t target_size = config_.max_cached_gifs / 2;
    while (cache_.size() > target_size) {
        EvictOldestCache();
    }

    ESP_LOGI(TAG, "Garbage collection completed, cache size: %zu", cache_.size());
}

void GifDecoderCache::PrintCacheStats() {
    ESP_LOGI(TAG, "=== GIF Decoder Cache Statistics ===");
    ESP_LOGI(TAG, "Cached GIFs: %zu / %lu", cache_.size(), config_.max_cached_gifs);
    ESP_LOGI(TAG, "Total decoded frames: %zu", stats_.total_decoded_frames);
    ESP_LOGI(TAG, "Total memory used: %zu bytes (%.1f MB)",
             stats_.total_memory_used, stats_.total_memory_used / (1024.0 * 1024.0));
    ESP_LOGI(TAG, "Decode requests: %zu", stats_.decode_requests);
    ESP_LOGI(TAG, "Decode successes: %zu", stats_.decode_successes);
    ESP_LOGI(TAG, "Decode failures: %zu", stats_.decode_failures);
    ESP_LOGI(TAG, "Cache hits: %zu", stats_.cache_hits);
    ESP_LOGI(TAG, "Cache misses: %zu", stats_.cache_misses);
    ESP_LOGI(TAG, "Average decode time: %lu us", stats_.avg_decode_time_us);
    ESP_LOGI(TAG, "Max decode time: %lu us", stats_.max_decode_time_us);

    if (stats_.cache_hits + stats_.cache_misses > 0) {
        float hit_rate = (float)stats_.cache_hits / (stats_.cache_hits + stats_.cache_misses) * 100;
        ESP_LOGI(TAG, "Cache hit rate: %.1f%%", hit_rate);
    }

    // 打印每个缓存的详细信息
    for (const auto& pair : cache_) {
        const auto& entry = pair.second;
        ESP_LOGI(TAG, "  '%s': %lu/%lu frames, %.1f KB, %s, last access: %lu ms ago",
                 entry->emotion_name.c_str(),
                 entry->cached_frames, entry->total_frames,
                 entry->total_memory / 1024.0,
                 entry->is_fully_cached ? "complete" : (entry->is_decoding ? "decoding" : "partial"),
                 (esp_timer_get_time() / 1000) - entry->last_access_time);
    }
}
