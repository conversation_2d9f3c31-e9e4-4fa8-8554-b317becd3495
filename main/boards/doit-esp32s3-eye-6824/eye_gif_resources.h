#pragma once

#include <lvgl.h>

// 声明src目录下的实际GIF资源
// 这些GIF资源已经通过工具转换为C数组格式

extern "C" {
    // 实际存在的GIF资源
    LV_IMG_DECLARE(staticstate);     // 静态/中性表情
    LV_IMG_DECLARE(happy);           // 开心表情
    LV_IMG_DECLARE(sad);             // 悲伤表情
    LV_IMG_DECLARE(anger);           // 愤怒表情
    LV_IMG_DECLARE(scare);           // 惊吓/惊讶表情
    LV_IMG_DECLARE(buxue);           // 不学/困惑/思考表情
    LV_IMG_DECLARE(heart_beat);  
    LV_IMG_DECLARE(moving);  
    LV_IMG_DECLARE(cute);  
    LV_IMG_DECLARE(purple_eye_240_240);
    LV_IMG_DECLARE(purple_eye_240_240_ro);
}
