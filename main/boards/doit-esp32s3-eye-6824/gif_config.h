#pragma once

// GIF显示性能配置参数

// LVGL性能配置 (紧急内存优化，修复堆栈问题)
#define GIF_LVGL_TASK_PRIORITY      2       // LVGL任务优先级 (低优先级节省资源)
#define GIF_LVGL_TIMER_PERIOD_MS    100     // LVGL刷新周期 (ms) - 100ms = 10FPS (节省CPU)
#define GIF_LVGL_TASK_STACK         5120    // LVGL任务堆栈大小 (增加以避免栈溢出)
#define GIF_LVGL_TASK_CORE          1       // LVGL任务绑定的核心 (0或1)

// GIF同步任务配置 (紧急内存优化，修复队列问题)
#define GIF_SYNC_TASK_PRIORITY      3       // GIF同步任务优先级 (适中优先级)
#define GIF_SYNC_TASK_STACK         3072    // GIF同步任务堆栈大小 (增加以避免栈溢出)
#define GIF_SYNC_TASK_CORE          0       // GIF同步任务绑定的核心 (与LVGL任务分离)
#define GIF_SYNC_QUEUE_SIZE         5       // GIF同步队列大小 (恢复到安全大小)

// 显示缓冲配置 (紧急内存优化)
#define GIF_BUFFER_DIVISOR          16      // 缓冲区大小除数 (1/16屏幕，最小缓冲区)
                                           // 16 = 1/16屏幕, 12 = 1/12屏幕
#define GIF_ENABLE_DOUBLE_BUFFER    false   // 禁用双缓冲减少内存压力
#define GIF_USE_DMA_BUFFER          false   // 不使用DMA缓冲区，使用PSRAM
#define GIF_USE_PSRAM_BUFFER        true    // 使用PSRAM缓冲区

// 同步超时配置 (优化栈溢出问题)
#define GIF_LVGL_LOCK_TIMEOUT_MS    1000    // LVGL锁超时时间 (ms) - 减少阻塞时间
#define GIF_QUEUE_SEND_TIMEOUT_MS   500     // 队列发送超时时间 (ms) - 减少阻塞时间

// 调试配置
#define GIF_DEBUG_MEMORY_USAGE      1       // 是否打印内存使用情况
#define GIF_DEBUG_SYNC_MESSAGES     1       // 是否打印同步消息调试信息
#define GIF_DEBUG_PERFORMANCE       1       // 是否打印性能调试信息

// 性能优化开关 (紧急内存优化)
// #define GIF_OPTIMIZE_FOR_MEMORY      1      // 启用内存优化 (紧急)
// #define GIF_CONSERVATIVE_MODE        1      // 启用保守模式 (稳定性优先)
#define GIF_OPTIMIZE_FOR_PERFORMANCE 1      // 启用性能优化

#if GIF_CONSERVATIVE_MODE
    // 保守模式配置 (稳定性优先，修复栈溢出)
    #undef GIF_LVGL_TASK_PRIORITY
    #define GIF_LVGL_TASK_PRIORITY      2       // 低优先级但不是最低
    #undef GIF_LVGL_TIMER_PERIOD_MS
    #define GIF_LVGL_TIMER_PERIOD_MS    100     // 10FPS，保守设置
    #undef GIF_SYNC_TASK_PRIORITY
    #define GIF_SYNC_TASK_PRIORITY      4       // 高于LVGL任务，确保同步优先
    #undef GIF_BUFFER_DIVISOR
    #define GIF_BUFFER_DIVISOR          10      // 1/10屏幕，适中缓冲区
    #undef GIF_ENABLE_DOUBLE_BUFFER
    #define GIF_ENABLE_DOUBLE_BUFFER    false   // 保守模式禁用双缓冲
    #undef GIF_LVGL_TASK_STACK
    #define GIF_LVGL_TASK_STACK         6144    // 6KB堆栈 (避免栈溢出)
    #undef GIF_SYNC_TASK_STACK
    #define GIF_SYNC_TASK_STACK         4096    // 4KB堆栈 (避免栈溢出)
#endif

#if GIF_OPTIMIZE_FOR_PERFORMANCE
    // 性能优化配置 (目标20FPS，内存平衡)
    #undef GIF_LVGL_TASK_PRIORITY
    #define GIF_LVGL_TASK_PRIORITY      4       // 高优先级
    #undef GIF_SYNC_TASK_PRIORITY
    #define GIF_SYNC_TASK_PRIORITY      5       // 更高优先级确保同步
    #undef GIF_BUFFER_DIVISOR
    #define GIF_BUFFER_DIVISOR          8       // 1/8屏幕，平衡性能和内存
    #undef GIF_ENABLE_DOUBLE_BUFFER
    #define GIF_ENABLE_DOUBLE_BUFFER    true    // 启用双缓冲提升性能
    #undef GIF_LVGL_TASK_STACK
    #define GIF_LVGL_TASK_STACK         6144    // 适中堆栈大小
    #undef GIF_LVGL_TIMER_PERIOD_MS
    #define GIF_LVGL_TIMER_PERIOD_MS    40      // 40ms = 25FPS 目标刷新率
#endif

#if GIF_OPTIMIZE_FOR_MEMORY
    // 平衡内存优化配置 (修复栈溢出问题)
    #undef GIF_BUFFER_DIVISOR
    #define GIF_BUFFER_DIVISOR          16      // 1/16屏幕，小缓冲区
    #undef GIF_ENABLE_DOUBLE_BUFFER
    #define GIF_ENABLE_DOUBLE_BUFFER    false   // 禁用双缓冲
    #undef GIF_LVGL_TASK_STACK
    #define GIF_LVGL_TASK_STACK         6144    // 6KB堆栈 (增加以避免栈溢出)
    #undef GIF_SYNC_TASK_STACK
    #define GIF_SYNC_TASK_STACK         4096    // 4KB堆栈 (增加以避免栈溢出)
    #undef GIF_LVGL_TIMER_PERIOD_MS
    #define GIF_LVGL_TIMER_PERIOD_MS    100     // 10FPS，平衡性能
    #undef GIF_SYNC_QUEUE_SIZE
    #define GIF_SYNC_QUEUE_SIZE         3       // 安全队列大小
#endif

// 编译时检查
#if GIF_SYNC_TASK_PRIORITY <= GIF_LVGL_TASK_PRIORITY
    #warning "GIF sync task priority should be higher than LVGL task priority"
#endif

#if GIF_SYNC_TASK_CORE == GIF_LVGL_TASK_CORE
    #warning "GIF sync task and LVGL task should run on different cores for better performance"
#endif
