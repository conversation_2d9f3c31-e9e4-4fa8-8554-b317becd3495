#include "wifi_board.h"
#include "config.h"  // 必须在条件编译之前包含，以确保DualScreen宏被正确定义
#ifdef DualScreen
#include "audio_codecs/es8311_audio_codec.h"
#else
#include "audio_codecs/no_audio_codec.h"
#include "audio_codecs/vb6824_audio_codec.h"
#endif
#include "display/lcd_display.h"
#include "system_reset.h"
#include "application.h"
#include "button.h"
#include "iot/thing_manager.h"
#include "led/single_led.h"
#include "power_save_timer.h"

#include <wifi_station.h>
#include <esp_log.h>
#include <driver/i2c_master.h>
#include <esp_lcd_panel_vendor.h>
#include <esp_lcd_panel_io.h>
#include <esp_lcd_panel_ops.h>
#include <driver/spi_common.h>
#include "display/eye_display.h"
#include <esp_lcd_gc9a01.h>
#include "eye_gif_display.h"

#define TAG "CompactWifiBoardLCD"

// 声明双屏全局变量（用于 EyeGifDisplay）
esp_lcd_panel_io_handle_t lcd_io_eye = NULL;
esp_lcd_panel_handle_t lcd_panel_eye = NULL;
esp_lcd_panel_io_handle_t lcd_io_eye2 = NULL;
esp_lcd_panel_handle_t lcd_panel_eye2 = NULL;

#if CONFIG_LCD_GC9A01_240X240
    LV_FONT_DECLARE(font_puhui_20_4);   
    LV_FONT_DECLARE(font_awesome_20_4); 
#elif CONFIG_LCD_GC9A01_160X160
    LV_FONT_DECLARE(font_puhui_14_1);   
    LV_FONT_DECLARE(font_awesome_14_1); 
#endif

class CompactWifiBoardLCD : public WifiBoard {
private:
    /* LCD IO and panel */
    esp_lcd_panel_io_handle_t lcd_io1 = NULL;
    esp_lcd_panel_handle_t lcd_panel1 = NULL;
    esp_lcd_panel_io_handle_t lcd_io2 = NULL;
    esp_lcd_panel_handle_t lcd_panel2 = NULL;

    Button boot_button_;
    EyeGifDisplay* display_;
    #ifdef DualScreen
    i2c_master_bus_handle_t codec_i2c_bus_;
    #else
    VbAduioCodec audio_codec;
    PowerSaveTimer* power_save_timer_;
    #endif

    #ifndef DualScreen
    void InitializePowerSaveTimer() {
        
        power_save_timer_ = new PowerSaveTimer(-1, 60, 300);
        power_save_timer_->OnEnterSleepMode([this]() {
            ESP_LOGI(TAG, "Enabling sleep mode");
            auto display = GetDisplay();
            display->SetChatMessage("system", "");
            display->SetEmotion("sleepy");
            #if CONFIG_LCD_GC9A01_160X160
                GetBacklight()->RestoreBrightness();
            #endif
            //gpio_set_level(SLEEP_GOIO, 0);

        });
        power_save_timer_->OnExitSleepMode([this]() {
            auto display = GetDisplay();
            display->SetChatMessage("system", "");
            display->SetEmotion("neutral");
            #if CONFIG_LCD_GC9A01_160X160
                GetBacklight()->RestoreBrightness();
            #endif
            //gpio_set_level(SLEEP_GOIO, 1);
        });
        power_save_timer_->OnShutdownRequest([this]() {
            //pmic_->PowerOff();
            //gpio_set_level(SLEEP_GOIO, 0);
            ESP_LOGI(TAG,"Not used for a long time. Shut down. Press and hold to turn on!");
            gpio_set_level(SLEEP_GOIO, 0);
        });
        power_save_timer_->SetEnabled(true);
        
    }

    // 初始化按钮
    void InitializeButtons() {
        // 当boot_button_被点击时，执行以下操作
        boot_button_.OnClick([this]() {
            // 获取应用程序实例
            auto& app = Application::GetInstance();
            // 如果设备状态为kDeviceStateStarting且WifiStation未连接，则重置Wifi配置
            if (app.GetDeviceState() == kDeviceStateStarting && !WifiStation::GetInstance().IsConnected()) {
                ESP_LOGI(TAG, "重新配网 onclick");
                ResetWifiConfiguration();
            }
            // 切换聊天状态
            app.ToggleChatState();
        });
         boot_button_.OnPressRepeat([this](uint16_t count) {
            ESP_LOGI(TAG,"boot button down");
            if(count >= 3){
                ESP_LOGI(TAG, "重新配网");
                ResetWifiConfiguration();
            }
        });
    }
    #endif

    // 物联网初始化，添加对 AI 可见设备
    void InitializeIot() {
        auto& thing_manager = iot::ThingManager::GetInstance();
        thing_manager.AddThing(iot::CreateThing("Speaker"));
        thing_manager.AddThing(iot::CreateThing("Screen"));
        // thing_manager.AddThing(iot::CreateThing("Lamp"));
    }

    // GC9A01-SPI1初始化-用于显示小智
    void InitializeSpiEye1() {
        const spi_bus_config_t buscfg = {
            .mosi_io_num = GC9A01_SPI1_LCD_GPIO_MOSI,
            .miso_io_num = GPIO_NUM_NC,
            .sclk_io_num = GC9A01_SPI1_LCD_GPIO_SCLK,
            .quadwp_io_num = GPIO_NUM_NC,
            .quadhd_io_num = GPIO_NUM_NC,
            .max_transfer_sz = DISPLAY_WIDTH * DISPLAY_HEIGHT * sizeof(uint16_t), // 增大传输大小,
        };
        ESP_ERROR_CHECK(spi_bus_initialize(DISPLAY_SPI1_NUM, &buscfg, SPI_DMA_CH_AUTO));
    }

    // GC9A01-SPI2初始化-用于显示魔眼
    void InitializeSpiEye2() {
        const spi_bus_config_t buscfg = {
            .mosi_io_num = GC9A01_SPI2_LCD_GPIO_MOSI,
            .miso_io_num = GPIO_NUM_NC,
            .sclk_io_num = GC9A01_SPI2_LCD_GPIO_SCLK,
            .quadwp_io_num = GPIO_NUM_NC,
            .quadhd_io_num = GPIO_NUM_NC,
            .max_transfer_sz = DISPLAY_WIDTH * DISPLAY_HEIGHT * sizeof(uint16_t),
        };
        ESP_ERROR_CHECK(spi_bus_initialize(DISPLAY_SPI2_NUM, &buscfg, SPI_DMA_CH_AUTO));
    }

    // GC9A01-SPI1初始化-用于显示小智
    void InitializeGc9a01DisplayEye1() {
        ESP_LOGI(TAG, "Init GC9A01 display1");

        const esp_lcd_panel_io_spi_config_t io_config = {
            .cs_gpio_num = GC9A01_SPI1_LCD_GPIO_CS,
            .dc_gpio_num = GC9A01_SPI1_LCD_GPIO_DC,
            .spi_mode = 0,
            .pclk_hz = GC9A01_LCD_PIXEL_CLK_HZ,
            .trans_queue_depth = 10,
            .lcd_cmd_bits = GC9A01_LCD_CMD_BITS,
            .lcd_param_bits = GC9A01_LCD_PARAM_BITS,
        };
        esp_lcd_new_panel_io_spi((esp_lcd_spi_bus_handle_t)DISPLAY_SPI1_NUM, &io_config, &lcd_io1);
        lcd_io_eye = lcd_io1;

        ESP_LOGD(TAG, "Install LCD1 driver");
        esp_lcd_panel_dev_config_t panel_config = {
            .reset_gpio_num = GC9A01_SPI1_LCD_GPIO_RST,
            .color_space = GC9A01_LCD_COLOR_SPACE,
            .bits_per_pixel = GC9A01_LCD_BITS_PER_PIXEL,
        };
        panel_config.rgb_endian = DISPLAY_RGB_ORDER;
        esp_lcd_new_panel_gc9a01(lcd_io1, &panel_config, &lcd_panel1);
        lcd_panel_eye = lcd_panel1;

        esp_lcd_panel_reset(lcd_panel1);
        esp_lcd_panel_init(lcd_panel1);
        esp_lcd_panel_invert_color(lcd_panel1, DISPLAY_COLOR_INVERT);
        esp_lcd_panel_disp_on_off(lcd_panel1, true);
    }

    // GC9A01-SPI2初始化-用于显示魔眼
    void InitializeGc9a01DisplayEye2() {
        ESP_LOGI(TAG, "Init GC9A01 display2");
        const esp_lcd_panel_io_spi_config_t io_config = {
            .cs_gpio_num = GC9A01_SPI2_LCD_GPIO_CS,
            .dc_gpio_num = GC9A01_SPI2_LCD_GPIO_DC,
            .spi_mode = 0,
            .pclk_hz = GC9A01_LCD_PIXEL_CLK_HZ,
            .trans_queue_depth = 10,
            .lcd_cmd_bits = GC9A01_LCD_CMD_BITS,
            .lcd_param_bits = GC9A01_LCD_PARAM_BITS,
        };
        esp_lcd_new_panel_io_spi((esp_lcd_spi_bus_handle_t)DISPLAY_SPI2_NUM, &io_config, &lcd_io2);
        lcd_io_eye2 = lcd_io2;

        ESP_LOGD(TAG, "Install LCD2 driver");
        esp_lcd_panel_dev_config_t panel_config = {
            .reset_gpio_num = GC9A01_SPI2_LCD_GPIO_RST,
            .color_space = GC9A01_LCD_COLOR_SPACE,
            .bits_per_pixel = GC9A01_LCD_BITS_PER_PIXEL
        };
        panel_config.rgb_endian = DISPLAY_RGB_ORDER;
        esp_lcd_new_panel_gc9a01(lcd_io2, &panel_config, &lcd_panel2);
        lcd_panel_eye2 = lcd_panel2;
        esp_lcd_panel_reset(lcd_panel2);
        esp_lcd_panel_init(lcd_panel2);
        esp_lcd_panel_invert_color(lcd_panel2, DISPLAY_COLOR_INVERT);
        esp_lcd_panel_disp_on_off(lcd_panel2, true);
    }

    //初始化双屏
    void InitializeDualScreenEye(){
        // 初始化第一块屏幕
        InitializeSpiEye1();
        InitializeSpiEye2();
        InitializeGc9a01DisplayEye1();
        InitializeGc9a01DisplayEye2();

        // 创建双屏 GIF 显示对象
        display_ = new EyeGifDisplay(
            DISPLAY_WIDTH, DISPLAY_HEIGHT, DISPLAY_OFFSET_X, DISPLAY_OFFSET_Y,
            DISPLAY_MIRROR_X, DISPLAY_MIRROR_Y, DISPLAY_SWAP_XY,
            {
                #if CONFIG_LCD_GC9A01_240X240
                    .text_font = &font_puhui_20_4,
                    .icon_font = &font_awesome_20_4,
                #elif CONFIG_LCD_GC9A01_160X160
                     .text_font = &font_puhui_14_1,
                     .icon_font = &font_awesome_14_1,
                #endif
                .emoji_font = font_emoji_64_init(),
            }
        );
    }

public:
    // CompactWifiBoardLCD() :
    //     boot_button_(BOOT_BUTTON_GPIO) {
    #ifdef DualScreen
      CompactWifiBoardLCD():boot_button_(BOOT_BUTTON_GPIO) {
        gpio_set_direction(AUDIO_CODEC_PA_PIN, GPIO_MODE_OUTPUT);
        gpio_set_pull_mode(AUDIO_CODEC_PA_PIN, GPIO_PULLDOWN_ONLY);
        gpio_set_level(AUDIO_CODEC_PA_PIN, 0); //初始化
        InitializeI2c();
        InitializeDualScreenEye();
        InitializeButtons();
        InitializeIot();
      }
    #else
      CompactWifiBoardLCD():boot_button_(BOOT_BUTTON_GPIO), audio_codec(CODEC_RX_GPIO, CODEC_TX_GPIO){
        #if CONFIG_LCD_GC9A01_160X160
            gpio_config_t bk_gpio_config = {
                    .pin_bit_mask = 1ULL << GC9A01_SPI1_LCD_GPIO_BL,
                    .mode = GPIO_MODE_OUTPUT,
                };
            ESP_ERROR_CHECK(gpio_config(&bk_gpio_config));
            ESP_ERROR_CHECK(gpio_set_level(GC9A01_SPI1_LCD_GPIO_BL, 1));
        #endif

        InitializeDualScreenEye();
        InitializeButtons();
        InitializeIot();

        gpio_set_pull_mode(SLEEP_GOIO, GPIO_PULLUP_ONLY);
        gpio_set_direction(SLEEP_GOIO, GPIO_MODE_OUTPUT);
        gpio_set_level(SLEEP_GOIO, 1);

        //InitializePowerSaveTimer();

        auto codec = GetAudioCodec();
        auto volume = codec->output_volume() + 100;
        if (volume > 100) {
            volume = 100;
        }
        codec->SetOutputVolume(volume);
         audio_codec.OnWakeUp([this](const std::string& command) {
            if (command == std::string(vb6824_get_wakeup_word())){
                if(Application::GetInstance().GetDeviceState() != kDeviceStateListening){
                    Application::GetInstance().WakeWordInvoke("你好小智");
                }
            }else if (command == "开始配网"){
                ResetWifiConfiguration();
            }
        });
    }
    #endif
        
    

    // virtual Led* GetLed() override {
    //     static SingleLed led(BUILTIN_LED_GPIO);
    //     return &led;
    // }
    #ifdef DualScreen
    void InitializeButtons() {
        boot_button_.OnClick([this]() {
            auto& app = Application::GetInstance();
            if (app.GetDeviceState() == kDeviceStateStarting && !WifiStation::GetInstance().IsConnected()) {
                ResetWifiConfiguration();
            }
            app.ToggleChatState();
        });
    }

     void InitializeI2c() {
        // Initialize I2C peripheral
        i2c_master_bus_config_t i2c_bus_cfg = {
            .i2c_port = I2C_NUM_0,
            .sda_io_num = AUDIO_CODEC_I2C_SDA_PIN,
            .scl_io_num = AUDIO_CODEC_I2C_SCL_PIN,
            .clk_source = I2C_CLK_SRC_DEFAULT,
            .glitch_ignore_cnt = 7,
            .intr_priority = 0,
            .trans_queue_depth = 0,
            .flags = {
                .enable_internal_pullup = 1,
            },
        };
        ESP_ERROR_CHECK(i2c_new_master_bus(&i2c_bus_cfg, &codec_i2c_bus_));
    }


    virtual AudioCodec* GetAudioCodec() override {
        static Es8311AudioCodec audio_codec(
        codec_i2c_bus_, 
        I2C_NUM_0, 
        AUDIO_INPUT_SAMPLE_RATE, 
        AUDIO_OUTPUT_SAMPLE_RATE,
        AUDIO_I2S_GPIO_MCLK, 
        AUDIO_I2S_GPIO_BCLK, 
        AUDIO_I2S_GPIO_WS, 
        AUDIO_I2S_GPIO_DOUT, 
        AUDIO_I2S_GPIO_DIN,
        AUDIO_CODEC_PA_PIN, 
        AUDIO_CODEC_ES8311_ADDR);
        return &audio_codec;
    }

    #else
    virtual AudioCodec* GetAudioCodec() override {
        return &audio_codec;
    }
    #endif


    virtual Display* GetDisplay() override {
        return display_;
    }


    #if CONFIG_LCD_GC9A01_160X160
        // 获取背光对象
        virtual Backlight* GetBacklight() override {
            if (GC9A01_SPI1_LCD_GPIO_BL != GPIO_NUM_NC) {
                static PwmBacklight backlight(GC9A01_SPI1_LCD_GPIO_BL, DISPLAY_BACKLIGHT_OUTPUT_INVERT);
                return &backlight;
            }
            return nullptr;
        }
    #endif
};

DECLARE_BOARD(CompactWifiBoardLCD);
