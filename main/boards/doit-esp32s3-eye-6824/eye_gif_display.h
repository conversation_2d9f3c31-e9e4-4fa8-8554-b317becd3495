#pragma once

#include <lvgl.h>
#include "display/lcd_display.h"
#include <esp_lvgl_port.h>


/**
 * @brief 魔眼GIF表情显示类
 *
 * 专为 doit-esp32s3-eye-6824 开发板设计的双屏 GIF 表情显示类
 * 特点：
 * - 支持双屏独立控制，显示不同的 GIF 动画
 * - 全屏显示 240x240 GIF 动画
 * - 只显示 GIF，不显示任何文字标签
 * - 继承 Display 基类，完全自定义双屏逻辑
 */
class EyeGifDisplay : public Display {
public:
    EyeGifDisplay(int width, int height, int offset_x, int offset_y, bool mirror_x,
                  bool mirror_y, bool swap_xy, DisplayFonts fonts);

    virtual ~EyeGifDisplay();

    // 重写基类方法 - 只处理表情显示
    virtual void SetEmotion(const char* emotion) override;
    // 重写基类方法 - 空实现，不显示任何文字
    virtual void SetChatMessage(const char* role, const char* content) override;
    virtual void SetIcon(const char* icon) override;
    virtual void SetStatus(const char* status) override;
    virtual void SetTheme(const std::string& theme_name) override;

    // 新的双屏控制接口
    void SetEmotionOnScreen(uint8_t screen_id, const char* emotion);
    void SetDualEmotion(const char* emotion1, const char* emotion2);
    void SetSyncEmotion(const char* emotion); // 两屏显示相同表情

    // 重写基类的锁机制
    virtual bool Lock(int timeout_ms = 100) override;
    virtual void Unlock() override;

    // 调试函数
    void DebugLVGLDisplays();

private:
    // 表情映射结构
    struct EmotionMap {
        const char* name;
        const lv_img_dsc_t* gif;
    };

    // 静态表情映射表
    static const EmotionMap emotion_maps_[];

    // 显示参数
    int width_, height_;
    int offset_x_, offset_y_;
    bool mirror_x_, mirror_y_, swap_xy_;
    DisplayFonts fonts_;

    // 双屏LVGL显示对象
    lv_display_t* display1_ = nullptr;
    lv_display_t* display2_ = nullptr;

    // 双屏屏幕对象
    lv_obj_t* screen1_ = nullptr;
    lv_obj_t* screen2_ = nullptr;

    // 双屏GIF组件
    lv_obj_t* emotion_gif1_ = nullptr;
    lv_obj_t* emotion_gif2_ = nullptr;

    // 屏幕句柄（通过外部全局变量获取）
    esp_lcd_panel_io_handle_t panel_io1_ = nullptr;
    esp_lcd_panel_handle_t panel1_ = nullptr;
    esp_lcd_panel_io_handle_t panel_io2_ = nullptr;
    esp_lcd_panel_handle_t panel2_ = nullptr;

    // 互斥锁
    SemaphoreHandle_t mutex_ = nullptr;

    // GIF同步控制
    TaskHandle_t sync_task_handle_ = nullptr;
    QueueHandle_t gif_sync_queue_ = nullptr;

    // GIF同步消息结构
    struct GifSyncMessage {
        const lv_img_dsc_t* gif_resource;
        bool sync_both_screens;
        uint8_t target_screen;  // 1 or 2, ignored if sync_both_screens is true
    };

    // 初始化方法
    void InitializeLVGL();
    void InitializeDualScreenDisplays();
    void SetupDualScreenGifContainers();

    // 配置初始化辅助方法
    lvgl_port_display_cfg_t CreateDisplayConfig(esp_lcd_panel_io_handle_t io_handle,
                                               esp_lcd_panel_handle_t panel_handle);
    lv_display_t* CreateAndConfigureDisplay(const lvgl_port_display_cfg_t& config,
                                           const char* display_name);

    // GIF同步任务
    static void GifSyncTask(void* parameter);
    void StartGifSyncTask();
    void StopGifSyncTask();

    // Event callback for GIF frame monitoring
    static void gif_frame_event_cb(lv_event_t * e);

    // 辅助方法
    const lv_img_dsc_t* GetGifResource(const char* emotion);
    void SetGifOnScreen(uint8_t screen_id, const lv_img_dsc_t* gif_dsc);
};
