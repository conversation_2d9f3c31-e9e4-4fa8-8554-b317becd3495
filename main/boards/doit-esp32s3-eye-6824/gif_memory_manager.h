#pragma once

#include "lvgl.h"
#include "esp_log.h"
#include "esp_heap_caps.h"
#include "esp_timer.h"
#include <unordered_map>
#include <string>

/**
 * GIF内存管理器
 * 专门处理GIF相关的内存分配和释放，防止内存泄漏
 */
class GifMemoryManager {
public:
    /**
     * 初始化GIF内存管理器
     */
    static void Initialize();

    /**
     * 安全地设置GIF源
     * 会先清理旧的GIF资源，再设置新的
     */
    static bool SafeSetGifSrc(lv_obj_t* gif_obj, const lv_img_dsc_t* gif_dsc, const char* emotion_name = nullptr);

    /**
     * 清理GIF对象的内存
     */
    static void CleanupGifObject(lv_obj_t* gif_obj);

    /**
     * 强制垃圾回收
     * 清理所有未使用的GIF缓存
     */
    static void ForceGarbageCollection();

    /**
     * 获取GIF内存使用统计
     */
    static void PrintGifMemoryStats();

    /**
     * 检查是否需要内存清理
     */
    static bool NeedsMemoryCleanup();

    /**
     * 设置内存清理阈值
     */
    static void SetCleanupThreshold(size_t internal_threshold, size_t psram_threshold);

private:
    static const char* TAG;
    static bool initialized_;
    
    // 内存清理阈值
    static size_t internal_cleanup_threshold_;
    static size_t psram_cleanup_threshold_;
    
    // GIF对象跟踪
    struct GifObjectInfo {
        std::string emotion_name;
        size_t memory_usage;
        uint32_t last_access_time;
    };
    
    static std::unordered_map<lv_obj_t*, GifObjectInfo> gif_objects_;
    
    // 统计信息
    struct Stats {
        size_t total_gif_objects;
        size_t total_memory_used;
        size_t cleanup_count;
        size_t failed_allocations;
    };
    
    static Stats stats_;
    
    /**
     * 估算GIF对象的内存使用
     */
    static size_t EstimateGifMemoryUsage(const lv_img_dsc_t* gif_dsc);
    
    /**
     * 清理最旧的GIF缓存
     */
    static void CleanupOldestGifCache();
};
