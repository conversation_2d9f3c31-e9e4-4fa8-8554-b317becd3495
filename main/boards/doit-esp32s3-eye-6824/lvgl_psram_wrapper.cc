#include "lvgl_psram_wrapper.h"
#include "memory_monitor.h"

const char* LVGLPSRAMWrapper::TAG = "LVGLPSRAMWrapper";
bool LVGLPSRAMWrapper::initialized_ = false;
bool LVGLPSRAMWrapper::batch_mode_ = false;
size_t LVGLPSRAMWrapper::objects_created_ = 0;

void LVGLPSRAMWrapper::Initialize() {
    if (initialized_) {
        return;
    }
    
    ESP_LOGI(TAG, "Initializing LVGL PSRAM Wrapper");
    
    // 这里可以设置LVGL的内存钩子，但由于我们已经修改了系统级分配策略，
    // 主要是提供便利的包装函数
    
    initialized_ = true;
    ESP_LOGI(TAG, "LVGL PSRAM Wrapper initialized");
}

lv_obj_t* LVGLPSRAMWrapper::CreateScreen() {
    if (!initialized_) {
        Initialize();
    }
    
    ESP_LOGI(TAG, "Creating screen with PSRAM optimization");
    
    // 拍摄内存快照
    auto before = MemoryMonitor::TakeSnapshot();
    
    // 创建屏幕对象
    lv_obj_t* screen = lv_obj_create(NULL);
    
    if (screen) {
        objects_created_++;
        ESP_LOGI(TAG, "Screen created: %p (total objects: %zu)", screen, objects_created_);
        
        // 设置默认样式以优化内存使用
        lv_obj_set_style_bg_color(screen, lv_color_black(), 0);
        lv_obj_set_style_border_width(screen, 0, 0);
        lv_obj_set_style_pad_all(screen, 0, 0);
    } else {
        ESP_LOGE(TAG, "Failed to create screen");
    }
    
    // 比较内存使用
    auto after = MemoryMonitor::TakeSnapshot();
    MemoryMonitor::CompareSnapshots(before, after, "CreateScreen");
    
    return screen;
}

lv_obj_t* LVGLPSRAMWrapper::CreateGif(lv_obj_t* parent) {
    if (!initialized_) {
        Initialize();
    }
    
    if (!parent) {
        ESP_LOGE(TAG, "Cannot create GIF: parent is null");
        return nullptr;
    }
    
    ESP_LOGI(TAG, "Creating GIF object with PSRAM optimization");
    
    // 拍摄内存快照
    auto before = MemoryMonitor::TakeSnapshot();
    
    // 在批量模式下，先进行垃圾回收
    if (batch_mode_ && objects_created_ % 5 == 0) {
        ForceCleanup();
    }
    
    // 创建GIF对象
    lv_obj_t* gif = lv_gif_create(parent);
    
    if (gif) {
        objects_created_++;
        ESP_LOGI(TAG, "GIF created: %p (total objects: %zu)", gif, objects_created_);
        
        // 优化GIF对象设置
        lv_obj_set_style_border_width(gif, 0, 0);
        lv_obj_set_style_bg_opa(gif, LV_OPA_TRANSP, 0);
        lv_obj_set_style_pad_all(gif, 0, 0);
        
        // 设置默认大小以避免后续重新分配
        lv_obj_set_size(gif, 160, 160); // 假设屏幕大小
    } else {
        ESP_LOGE(TAG, "Failed to create GIF object");
        
        // 创建失败时尝试垃圾回收后重试
        ESP_LOGW(TAG, "Attempting garbage collection and retry...");
        ForceCleanup();
        PSRAMForceAllocator::GarbageCollect();
        
        gif = lv_gif_create(parent);
        if (gif) {
            ESP_LOGI(TAG, "GIF created after cleanup: %p", gif);
            objects_created_++;
        } else {
            ESP_LOGE(TAG, "GIF creation failed even after cleanup");
        }
    }
    
    // 比较内存使用
    auto after = MemoryMonitor::TakeSnapshot();
    MemoryMonitor::CompareSnapshots(before, after, "CreateGif");
    
    return gif;
}

void LVGLPSRAMWrapper::SafeDeleteObject(lv_obj_t* obj) {
    if (!obj) {
        return;
    }
    
    ESP_LOGI(TAG, "Safely deleting LVGL object: %p", obj);
    
    // 拍摄内存快照
    auto before = MemoryMonitor::TakeSnapshot();
    
    // 如果是GIF对象，先清理GIF资源
    // 注意：在LVGL 9.x中，我们使用更安全的方法来检查和清理GIF对象
    ESP_LOGD(TAG, "Attempting to clean up potential GIF object resources");
    // 尝试设置空源以释放GIF缓存（如果是GIF对象的话）
    // 这个调用对非GIF对象是安全的，会被忽略
    lv_gif_set_src(obj, nullptr);
    
    // 删除对象
    lv_obj_del(obj);
    
    if (objects_created_ > 0) {
        objects_created_--;
    }
    
    // 比较内存使用
    auto after = MemoryMonitor::TakeSnapshot();
    MemoryMonitor::CompareSnapshots(before, after, "SafeDeleteObject");
    
    ESP_LOGI(TAG, "Object deleted (remaining objects: %zu)", objects_created_);
}

void LVGLPSRAMWrapper::BeginBatchCreate() {
    ESP_LOGI(TAG, "Beginning batch object creation mode");
    batch_mode_ = true;
    
    // 预先进行一次垃圾回收
    ForceCleanup();
}

void LVGLPSRAMWrapper::EndBatchCreate() {
    ESP_LOGI(TAG, "Ending batch object creation mode");
    batch_mode_ = false;
    
    // 批量创建结束后进行清理
    ForceCleanup();
    
    ESP_LOGI(TAG, "Batch creation completed. Total objects: %zu", objects_created_);
}

void LVGLPSRAMWrapper::ForceCleanup() {
    ESP_LOGI(TAG, "Performing LVGL cleanup");
    
    // 记录清理前的内存状态
    auto before = MemoryMonitor::TakeSnapshot();
    
    // LVGL内部清理 - 使用LVGL 9.x兼容的API
    lv_obj_t* active_screen = lv_scr_act();
    if (active_screen) {
        lv_obj_invalidate(active_screen);
    }

    // 强制LVGL重绘和清理
    lv_refr_now(NULL);
    
    // 调用PSRAM分配器的垃圾回收
    PSRAMForceAllocator::GarbageCollect();
    
    // 记录清理后的内存状态
    auto after = MemoryMonitor::TakeSnapshot();
    MemoryMonitor::CompareSnapshots(before, after, "ForceCleanup");
}

void LVGLPSRAMWrapper::PrintLVGLMemoryStats() {
    ESP_LOGI(TAG, "=== LVGL Memory Statistics ===");
    ESP_LOGI(TAG, "Objects created: %zu", objects_created_);
    ESP_LOGI(TAG, "Batch mode: %s", batch_mode_ ? "enabled" : "disabled");
    
    // 打印PSRAM分配器统计
    PSRAMForceAllocator::PrintStats();
    
    // 打印当前内存状态
    MemoryMonitor::PrintDetailedMemoryInfo();
}

void* LVGLPSRAMWrapper::PSRAMAllocHook(size_t size) {
    // 这个函数可以作为LVGL的内存分配钩子
    return PSRAMForceAllocator::Malloc(size);
}

void LVGLPSRAMWrapper::PSRAMFreeHook(void* ptr) {
    // 这个函数可以作为LVGL的内存释放钩子
    PSRAMForceAllocator::Free(ptr);
}
