#pragma once

#include "gif_decoder_cache.h"
#include "lvgl.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/timers.h"

/**
 * 高性能GIF播放器
 * 使用预解码缓存实现流畅的GIF播放
 */
class HighPerfGifPlayer {
public:
    /**
     * 播放器状态
     */
    enum class PlayState {
        STOPPED,
        PLAYING,
        PAUSED,
        LOADING
    };

    /**
     * 播放器配置
     */
    struct PlayerConfig {
        bool auto_preload = true;           // 自动预加载
        bool loop_playback = true;          // 循环播放
        uint32_t preload_frames = 10;       // 预加载帧数
        uint32_t max_fps = 20;              // 最大帧率
        bool sync_to_gif_timing = true;     // 同步到GIF原始时序
        bool adaptive_quality = true;       // 自适应质量
    };

    /**
     * 初始化高性能GIF播放器
     */
    static void Initialize(const PlayerConfig& config = PlayerConfig{});

    /**
     * 创建GIF播放器实例
     * @param canvas LVGL画布对象
     * @param width 播放区域宽度
     * @param height 播放区域高度
     * @return 播放器ID，失败返回-1
     */
    static int CreatePlayer(lv_obj_t* canvas, uint32_t width, uint32_t height);

    /**
     * 销毁播放器实例
     */
    static void DestroyPlayer(int player_id);

    /**
     * 加载GIF到播放器
     * @param player_id 播放器ID
     * @param gif_dsc GIF描述符
     * @param emotion_name 表情名称
     * @return 是否成功开始加载
     */
    static bool LoadGif(int player_id, const lv_img_dsc_t* gif_dsc, const char* emotion_name);

    /**
     * 开始播放
     */
    static bool StartPlayback(int player_id);

    /**
     * 暂停播放
     */
    static bool PausePlayback(int player_id);

    /**
     * 停止播放
     */
    static bool StopPlayback(int player_id);

    /**
     * 跳转到指定帧
     */
    static bool SeekToFrame(int player_id, uint32_t frame_index);

    /**
     * 设置播放速度倍率
     */
    static bool SetPlaybackSpeed(int player_id, float speed_multiplier);

    /**
     * 获取播放状态
     */
    static PlayState GetPlayState(int player_id);

    /**
     * 获取当前帧索引
     */
    static uint32_t GetCurrentFrame(int player_id);

    /**
     * 获取总帧数
     */
    static uint32_t GetTotalFrames(int player_id);

    /**
     * 获取加载进度 (0-100)
     */
    static uint32_t GetLoadProgress(int player_id);

    /**
     * 检查是否准备好播放
     */
    static bool IsReadyToPlay(int player_id);

    /**
     * 获取播放统计信息
     */
    static void PrintPlayerStats(int player_id);

    /**
     * 清理所有播放器
     */
    static void CleanupAllPlayers();

private:
    /**
     * 播放器实例
     */
    struct PlayerInstance {
        int id;
        lv_obj_t* canvas;
        uint32_t width;
        uint32_t height;
        std::string current_emotion;
        PlayState state;
        uint32_t current_frame;
        uint32_t total_frames;
        float speed_multiplier;
        uint32_t last_frame_time;
        uint32_t frame_interval_ms;
        TimerHandle_t frame_timer;
        bool is_ready;
        
        // 统计信息
        uint32_t frames_played;
        uint32_t dropped_frames;
        uint32_t avg_frame_time_us;
        uint32_t last_render_time_us;
    };

    static const char* TAG;
    static bool initialized_;
    static PlayerConfig config_;
    static std::vector<std::unique_ptr<PlayerInstance>> players_;
    static int next_player_id_;

    /**
     * 帧定时器回调
     */
    static void FrameTimerCallback(TimerHandle_t timer);

    /**
     * 渲染当前帧
     */
    static bool RenderFrame(PlayerInstance* player);

    /**
     * 更新播放器状态
     */
    static void UpdatePlayerState(PlayerInstance* player);

    /**
     * 计算帧间隔
     */
    static uint32_t CalculateFrameInterval(const GifDecoderCache::DecodedFrame* frame);

    /**
     * 查找播放器实例
     */
    static PlayerInstance* FindPlayer(int player_id);

    /**
     * 创建帧定时器
     */
    static TimerHandle_t CreateFrameTimer(int player_id);

    /**
     * 销毁帧定时器
     */
    static void DestroyFrameTimer(TimerHandle_t timer);
};
