#pragma once

#include "esp_heap_caps.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

/**
 * 内存监控工具类
 * 用于诊断内存分配问题和检测内存泄漏
 */
class MemoryMonitor {
public:
    struct MemorySnapshot {
        size_t free_internal;
        size_t free_psram;
        size_t free_dma;
        size_t min_free_internal;
        size_t min_free_psram;
        uint32_t timestamp;
    };

    /**
     * 初始化内存监控
     */
    static void Initialize();

    /**
     * 拍摄内存快照
     */
    static MemorySnapshot TakeSnapshot();

    /**
     * 比较两个快照并打印差异
     */
    static void CompareSnapshots(const MemorySnapshot& before, 
                               const MemorySnapshot& after, 
                               const char* operation_name);

    /**
     * 打印详细的内存使用情况
     */
    static void PrintDetailedMemoryInfo();

    /**
     * 检测内存泄漏
     * @param threshold_bytes 泄漏阈值（字节）
     * @return true如果检测到泄漏
     */
    static bool DetectMemoryLeak(size_t threshold_bytes = 1024);

    /**
     * 启动内存监控任务
     * @param monitor_interval_ms 监控间隔（毫秒）
     */
    static void StartMonitoringTask(uint32_t monitor_interval_ms = 10000);

    /**
     * 停止内存监控任务
     */
    static void StopMonitoringTask();

    /**
     * 测试内存分配行为
     */
    static void TestMemoryAllocation();

private:
    static MemorySnapshot baseline_snapshot_;
    static TaskHandle_t monitor_task_handle_;
    static bool monitoring_enabled_;
    static const char* TAG;

    static void MonitoringTask(void* parameter);
};
