#include "memory_test.h"
#include "memory_monitor.h"
#include "esp_log.h"
#include "esp_heap_caps.h"
#include "lvgl.h"
#include <cstring>

static const char* TAG = "MemoryTest";

void MemoryTest::RunAllTests() {
    ESP_LOGI(TAG, "=== Starting Memory Configuration Tests ===");
    
    TestBasicAllocation();
    TestLVGLAllocation();
    TestLargeAllocation();
    TestMemoryPools();
    
    ESP_LOGI(TAG, "=== Memory Tests Completed ===");
}

void MemoryTest::TestBasicAllocation() {
    ESP_LOGI(TAG, "--- Test 1: Basic Memory Allocation ---");
    
    auto before = MemoryMonitor::TakeSnapshot();
    
    // 测试小内存分配
    void* small_ptr = malloc(1024);
    ESP_LOGI(TAG, "malloc(1024) = %p", small_ptr);
    
    // 测试中等内存分配
    void* medium_ptr = malloc(8192);
    ESP_LOGI(TAG, "malloc(8192) = %p", medium_ptr);
    
    // 测试大内存分配
    void* large_ptr = malloc(64 * 1024);
    ESP_LOGI(TAG, "malloc(64KB) = %p", large_ptr);
    
    auto after_alloc = MemoryMonitor::TakeSnapshot();
    MemoryMonitor::CompareSnapshots(before, after_alloc, "Basic Allocation");
    
    // 释放内存
    if (small_ptr) free(small_ptr);
    if (medium_ptr) free(medium_ptr);
    if (large_ptr) free(large_ptr);
    
    auto after_free = MemoryMonitor::TakeSnapshot();
    MemoryMonitor::CompareSnapshots(after_alloc, after_free, "Basic Free");
}

void MemoryTest::TestLVGLAllocation() {
    ESP_LOGI(TAG, "--- Test 2: LVGL Memory Allocation ---");
    
    auto before = MemoryMonitor::TakeSnapshot();
    
    // 创建LVGL对象来测试LVGL内存分配器
    lv_obj_t* test_obj = lv_obj_create(NULL);
    if (test_obj) {
        ESP_LOGI(TAG, "LVGL object created: %p", test_obj);
        
        // 设置一些属性来触发更多内存分配
        lv_obj_set_size(test_obj, 100, 100);
        lv_obj_set_style_bg_color(test_obj, lv_color_hex(0xFF0000), 0);
        
        auto after_create = MemoryMonitor::TakeSnapshot();
        MemoryMonitor::CompareSnapshots(before, after_create, "LVGL Object Creation");
        
        // 删除对象
        lv_obj_del(test_obj);
        
        auto after_delete = MemoryMonitor::TakeSnapshot();
        MemoryMonitor::CompareSnapshots(after_create, after_delete, "LVGL Object Deletion");
    } else {
        ESP_LOGE(TAG, "Failed to create LVGL object");
    }
}

void MemoryTest::TestLargeAllocation() {
    ESP_LOGI(TAG, "--- Test 3: Large Memory Allocation ---");
    
    auto before = MemoryMonitor::TakeSnapshot();
    
    // 测试大块内存分配（应该使用PSRAM）
    size_t sizes[] = {32*1024, 64*1024, 128*1024, 256*1024, 512*1024};
    void* ptrs[5] = {nullptr};
    
    for (int i = 0; i < 5; i++) {
        ptrs[i] = malloc(sizes[i]);
        ESP_LOGI(TAG, "malloc(%zuKB) = %p", sizes[i]/1024, ptrs[i]);
        
        if (ptrs[i]) {
            // 写入一些数据来确保内存真正分配
            memset(ptrs[i], 0xAA, sizes[i]);
        }
    }
    
    auto after_alloc = MemoryMonitor::TakeSnapshot();
    MemoryMonitor::CompareSnapshots(before, after_alloc, "Large Allocation");
    
    // 释放内存
    for (int i = 0; i < 5; i++) {
        if (ptrs[i]) {
            free(ptrs[i]);
        }
    }
    
    auto after_free = MemoryMonitor::TakeSnapshot();
    MemoryMonitor::CompareSnapshots(after_alloc, after_free, "Large Free");
}

void MemoryTest::TestMemoryPools() {
    ESP_LOGI(TAG, "--- Test 4: Memory Pool Allocation ---");
    
    auto before = MemoryMonitor::TakeSnapshot();
    
    // 强制内部RAM分配
    void* internal_ptr = heap_caps_malloc(4096, MALLOC_CAP_INTERNAL);
    ESP_LOGI(TAG, "heap_caps_malloc(4KB, INTERNAL) = %p", internal_ptr);
    
    // 强制PSRAM分配
    void* psram_ptr = heap_caps_malloc(4096, MALLOC_CAP_SPIRAM);
    ESP_LOGI(TAG, "heap_caps_malloc(4KB, SPIRAM) = %p", psram_ptr);
    
    // 强制DMA兼容分配
    void* dma_ptr = heap_caps_malloc(4096, MALLOC_CAP_DMA);
    ESP_LOGI(TAG, "heap_caps_malloc(4KB, DMA) = %p", dma_ptr);
    
    auto after_alloc = MemoryMonitor::TakeSnapshot();
    MemoryMonitor::CompareSnapshots(before, after_alloc, "Pool Allocation");
    
    // 验证分配位置
    if (internal_ptr) {
        ESP_LOGI(TAG, "Internal allocation successful");
        heap_caps_free(internal_ptr);
    }
    
    if (psram_ptr) {
        ESP_LOGI(TAG, "PSRAM allocation successful");
        heap_caps_free(psram_ptr);
    }
    
    if (dma_ptr) {
        ESP_LOGI(TAG, "DMA allocation successful");
        heap_caps_free(dma_ptr);
    }
    
    auto after_free = MemoryMonitor::TakeSnapshot();
    MemoryMonitor::CompareSnapshots(after_alloc, after_free, "Pool Free");
}

void MemoryTest::TestMemoryLeakDetection() {
    ESP_LOGI(TAG, "--- Test 5: Memory Leak Detection ---");
    
    // 故意创建内存泄漏
    void* leak_ptr = malloc(1024);
    ESP_LOGI(TAG, "Created intentional leak: %p", leak_ptr);
    
    // 检测泄漏
    bool leak_detected = MemoryMonitor::DetectMemoryLeak(512);
    ESP_LOGI(TAG, "Leak detection result: %s", leak_detected ? "DETECTED" : "NOT DETECTED");
    
    // 清理泄漏
    if (leak_ptr) {
        free(leak_ptr);
        ESP_LOGI(TAG, "Cleaned up leak");
    }
}

void MemoryTest::PrintCurrentConfiguration() {
    ESP_LOGI(TAG, "=== Current Memory Configuration ===");
    
    // 检查PSRAM配置
    size_t psram_size = heap_caps_get_total_size(MALLOC_CAP_SPIRAM);
    ESP_LOGI(TAG, "PSRAM total size: %zu bytes (%.1f MB)", psram_size, psram_size / (1024.0 * 1024.0));
    
    // 检查内部RAM配置
    size_t internal_size = heap_caps_get_total_size(MALLOC_CAP_INTERNAL);
    ESP_LOGI(TAG, "Internal RAM total size: %zu bytes (%.1f KB)", internal_size, internal_size / 1024.0);
    
    // 检查DMA配置
    size_t dma_size = heap_caps_get_total_size(MALLOC_CAP_DMA);
    ESP_LOGI(TAG, "DMA capable memory: %zu bytes (%.1f KB)", dma_size, dma_size / 1024.0);
    
    // 打印详细信息
    MemoryMonitor::PrintDetailedMemoryInfo();
}
