## IDF Component Manager Manifest File
dependencies:
  waveshare/esp_lcd_sh8601: "1.0.2"
  espressif/esp_lcd_ili9341: "==1.2.0"
  espressif/esp_lcd_gc9a01: "==2.0.1"
  espressif/esp_lcd_st77916: "^1.0.1"
  espressif/esp_lcd_st7796:
    version: "==1.3.2"
    rules:
      - if: "target in [esp32, esp32s2, esp32s3, esp32p4]"
  espressif/esp_lcd_spd2010: "==1.0.2"
  espressif/esp_io_expander_tca9554: "==2.0.0"
  espressif/esp_lcd_panel_io_additions: "^1.0.1"
  78/esp_lcd_nv3023: "~1.0.0"
  78/esp-wifi-connect: "~2.4.2"
  78/esp-opus-encoder: "~2.3.2"
  78/esp-ml307: "~2.0.0"
  78/xiaozhi-fonts: "~1.3.2"
  78/esp-opus: ^1.0.5
  espressif/led_strip: "^2.5.5"
  espressif/esp_codec_dev: "~1.3.2"
  espressif/esp-sr: 
    version: "~2.1.1"
    rules:
      - if: "target in [esp32, esp32s3]"
  espressif/button: "~4.1.3"
  espressif/knob: "^1.0.0"
  espressif/esp_lcd_touch_ft5x06: "~1.0.7"
  lvgl/lvgl: "~9.2.2"
  esp_lvgl_port: "~2.6.0"
  espressif/esp_io_expander_tca95xx_16bit: "^2.0.0"
  tny-robotics/sh1106-esp-idf:
    version: "^1.0.0"
    rules:
      - if: 'idf_version >= "5.4.0"'
  ## Required IDF version
  idf:
    version: ">=5.3"

