
#include <freertos/FreeRTOS.h>
#include <freertos/event_groups.h>
#include <freertos/task.h>
 #include "esp_lcd_panel_ops.h"

#if !defined(IRIS_MAX)
#define MACRO
#define IRIS_MAX 280
#endif // MACRO
#if !defined(IRIS_MIN)
#define MACRO
#define IRIS_MIN 180
#endif // MACRO

#define  LINES_PER_BATCH 10 //缓冲区的行数为10行

#define NOBLINK 0     // Not currently engaged in a blink
#define ENBLINK 1     // Eyelid is currently closing
#define DEBLINK 2     // Eyelid is currently opening
#define BUFFER_SIZE 1024 // 64 to 512 seems optimum = 30 fps for default eye

#define NUM_EYES (1)    //定义眼睛数量
#if CONFIG_LCD_GC9A01_240X240
    #define DISPLAY_SIZE 240    //显示尺寸
#elif CONFIG_LCD_GC9A01_160X160
    #define DISPLAY_SIZE 160    //
#endif

extern bool is_blink;   //眨眼
extern bool is_track;   //跟踪
extern int16_t eyeNewX, eyeNewY;    // 新的眼睛位置数据

#if CONFIG_USE_EYE_STYLE_ES8311
    extern esp_lcd_panel_io_handle_t lcd_io_eye2;
    extern esp_lcd_panel_handle_t lcd_panel_eye2;
 #endif
    extern esp_lcd_panel_io_handle_t lcd_io_eye;
    extern esp_lcd_panel_handle_t lcd_panel_eye;


extern const uint16_t *sclera;
extern const uint16_t *iris;

extern TaskHandle_t task_update_eye_handler;   //魔眼更新任务的句柄

//函数声明
void split(
    int16_t  startValue, // Iris scale value (IRIS_MIN to IRIS_MAX) at start
    int16_t  endValue,   // Iris scale value at end
    uint64_t startTime,  // Use esp_timer_get_time() for timing
    int32_t  duration,   // Start-to-end time, in microseconds
    int16_t  range);
void frame(uint16_t iScale);
void drawEye(uint8_t e, uint32_t iScale, uint32_t scleraX, uint32_t scleraY, uint32_t uT, uint32_t lT) ;
esp_err_t esp_lcd_safe_draw_bitmap(esp_lcd_panel_handle_t panel,    
    int x_start,
    int y_start,
    int x_end,
    int y_end,
    const void *color_data);
int map1(int x, int in_min, int in_max, int out_min, int out_max);
//生成一个在 [min, max] 范围内的随机整数。它确保生成的随机数是均匀分布的，并且包含边界值 min 和 max。
int my_random(int min, int max);
int my_random1(int max);


 void task_eye_update(void *pvParameters);



